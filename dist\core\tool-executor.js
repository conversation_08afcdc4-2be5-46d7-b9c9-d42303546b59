import { ArienError } from '../types/index.js';
import { BashTool } from '../tools/bash.js';
import { GrepTool } from '../tools/grep.js';
import { GlobTool } from '../tools/glob.js';
import { WriteTool } from '../tools/write.js';
import { EditTool } from '../tools/edit.js';
import { WebTool } from '../tools/web.js';
import { RetryManager } from './retry-logic.js';
export class ToolExecutor {
    bashTool;
    grepTool;
    globTool;
    writeTool;
    editTool;
    webTool;
    retryManager;
    constructor() {
        this.bashTool = new BashTool();
        this.grepTool = new GrepTool();
        this.globTool = new GlobTool();
        this.writeTool = new WriteTool();
        this.editTool = new EditTool();
        this.webTool = new WebTool();
        this.retryManager = new RetryManager();
    }
    async executeTool(toolCall, context) {
        try {
            // Validate tool parameters
            if (!this.validateToolCall(toolCall)) {
                throw new ArienError(`Invalid parameters for tool: ${toolCall.type}`, 'INVALID_TOOL_PARAMS');
            }
            // Execute the tool with retry logic
            return await this.retryManager.executeWithRetry(async () => {
                switch (toolCall.type) {
                    case 'bash':
                        return await this.bashTool.execute(toolCall.parameters, context);
                    case 'grep':
                        return await this.grepTool.execute(toolCall.parameters);
                    case 'glob':
                        return await this.globTool.execute(toolCall.parameters);
                    case 'write':
                        return await this.writeTool.execute(toolCall.parameters);
                    case 'edit':
                        return await this.editTool.execute(toolCall.parameters);
                    case 'web':
                        return await this.webTool.execute(toolCall.parameters);
                    default:
                        throw new ArienError(`Unknown tool type: ${toolCall.type}`, 'UNKNOWN_TOOL_TYPE');
                }
            }, {
                maxAttempts: 2, // Most tools don't need many retries
                baseDelay: 1000,
                exponentialBase: 2
            });
        }
        catch (error) {
            return {
                id: toolCall.id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                executionTime: 0
            };
        }
    }
    async executeToolsParallel(toolCalls, context) {
        // Execute tools in parallel for independent operations
        const promises = toolCalls.map(toolCall => this.executeTool(toolCall, context));
        return await Promise.all(promises);
    }
    async executeToolsSequential(toolCalls, context) {
        // Execute tools sequentially for dependent operations
        const results = [];
        for (const toolCall of toolCalls) {
            const result = await this.executeTool(toolCall, context);
            results.push(result);
            // If a critical tool fails, we might want to stop execution
            if (!result.success && this.isCriticalTool(toolCall.type)) {
                break;
            }
        }
        return results;
    }
    validateToolCall(toolCall) {
        switch (toolCall.type) {
            case 'bash':
                return this.bashTool.validateParams(toolCall.parameters);
            case 'grep':
                return this.grepTool.validateParams(toolCall.parameters);
            case 'glob':
                return this.globTool.validateParams(toolCall.parameters);
            case 'write':
                return this.writeTool.validateParams(toolCall.parameters);
            case 'edit':
                return this.editTool.validateParams(toolCall.parameters);
            case 'web':
                return this.webTool.validateParams(toolCall.parameters);
            default:
                return false;
        }
    }
    isCriticalTool(toolType) {
        // Define which tools are critical and should stop execution on failure
        const criticalTools = ['bash', 'write', 'edit'];
        return criticalTools.includes(toolType);
    }
    getToolDescription(toolType) {
        switch (toolType) {
            case 'bash':
                return this.bashTool.getDescription();
            case 'grep':
                return this.grepTool.getDescription();
            case 'glob':
                return this.globTool.getDescription();
            case 'write':
                return this.writeTool.getDescription();
            case 'edit':
                return this.editTool.getDescription();
            case 'web':
                return this.webTool.getDescription();
            default:
                return 'Unknown tool';
        }
    }
    getAllToolDescriptions() {
        return {
            bash: this.bashTool.getDescription(),
            grep: this.grepTool.getDescription(),
            glob: this.globTool.getDescription(),
            write: this.writeTool.getDescription(),
            edit: this.editTool.getDescription(),
            web: this.webTool.getDescription()
        };
    }
    // Utility method to determine execution strategy
    determineExecutionStrategy(toolCalls) {
        // If any tool depends on file system state changes, use sequential
        const fileModifyingTools = ['write', 'edit', 'bash'];
        const hasFileModification = toolCalls.some(call => fileModifyingTools.includes(call.type));
        // If tools are reading from the same sources, use sequential to avoid conflicts
        const readingTools = ['grep', 'glob'];
        const hasMultipleReads = toolCalls.filter(call => readingTools.includes(call.type)).length > 1;
        // If there are bash commands, they might depend on each other
        const hasBashCommands = toolCalls.some(call => call.type === 'bash');
        if (hasFileModification || hasMultipleReads || hasBashCommands) {
            return 'sequential';
        }
        return 'parallel';
    }
    // Utility method to estimate execution time
    estimateExecutionTime(toolCalls) {
        const timeEstimates = {
            bash: 5000, // 5 seconds for bash commands
            grep: 2000, // 2 seconds for file search
            glob: 1000, // 1 second for file pattern matching
            write: 500, // 0.5 seconds for file writing
            edit: 1000, // 1 second for file editing
            web: 10000 // 10 seconds for web requests
        };
        const strategy = this.determineExecutionStrategy(toolCalls);
        if (strategy === 'parallel') {
            // Return the maximum time (longest running tool)
            return Math.max(...toolCalls.map(call => timeEstimates[call.type] || 1000));
        }
        else {
            // Return the sum of all times
            return toolCalls.reduce((total, call) => total + (timeEstimates[call.type] || 1000), 0);
        }
    }
}
//# sourceMappingURL=tool-executor.js.map