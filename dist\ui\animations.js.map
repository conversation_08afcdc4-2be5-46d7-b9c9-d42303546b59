{"version": 3, "file": "animations.js", "sourceRoot": "", "sources": ["../../src/ui/animations.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,OAAO,gBAAgB;IACnB,gBAAgB,GAA0B,IAAI,CAAC;IAC/C,UAAU,GAAG,CAAC,CAAC;IACf,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE/B,6CAA6C;IAC5B,UAAU,GAAG;QAC5B,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;KACX,CAAC;IAEe,aAAa,GAAG;QAC/B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;KACjD,CAAC;IAEe,UAAU,GAAG;QAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;KACjD,CAAC;IAEe,cAAc,GAAG;QAChC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI;KAC5B,CAAC;IAEF,KAAK,CAAC,OAAiD,MAAM,EAAE,OAAO,GAAG,EAAE;QACzE,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YACtD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;YAEjE,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,OAAO,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC;YAC3E,CAAC;iBAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC/B,OAAO,GAAG,GAAG,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC;YAC9F,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC;YAC3E,CAAC;YAED,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,cAAc,CAAC;YAC7B;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,YAAY,CAAC,OAAe,EAAE,KAAa,EAAE,OAAO,GAAG,EAAE;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,EAAE,GAAG,MAAM,CAAC;QAE1B,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;QAEvE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC;QAErC,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,CAAC,OAAe;QACvB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,QAAQ,CAAC,OAAe;QACtB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,KAAa,EAAE,OAAe;QACnD,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,SAAS;QACP,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,OAAO;QACL,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;CACF"}