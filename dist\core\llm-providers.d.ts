import { Config, LLMResponse, Message } from '../types/index.js';
import { RetryManager } from './retry-logic.js';
export declare abstract class LLMProvider {
    protected config: Config;
    protected retryManager: RetryManager;
    constructor(config: Config);
    abstract generateResponse(messages: Message[]): Promise<LLMResponse>;
    abstract validateConnection(): Promise<boolean>;
}
export declare class DeepseekProvider extends LLMProvider {
    private client;
    constructor(config: Config);
    generateResponse(messages: Message[]): Promise<LLMResponse>;
    validateConnection(): Promise<boolean>;
    private formatMessages;
    private parseResponse;
    private getToolDefinitions;
}
export declare class <PERSON>llama<PERSON>rovider extends LL<PERSON>rovider {
    private client;
    constructor(config: Config);
    generateResponse(messages: Message[]): Promise<LLMResponse>;
    validateConnection(): Promise<boolean>;
    private formatMessages;
    private parseResponse;
    private getToolDefinitions;
}
export declare function createLLMProvider(config: Config): LLMProvider;
//# sourceMappingURL=llm-providers.d.ts.map