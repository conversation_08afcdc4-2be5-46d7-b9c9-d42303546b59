import Conf from 'conf';
import { ConfigSchema } from '../types/index.js';
export class Settings {
    config;
    constructor() {
        this.config = new Conf({
            projectName: 'arien-ai-cli',
            schema: {
                provider: {
                    type: 'string',
                    enum: ['deepseek', 'ollama'],
                    default: 'deepseek'
                },
                apiKey: {
                    type: 'string'
                },
                model: {
                    type: 'string',
                    default: 'deepseek-chat'
                },
                ollamaUrl: {
                    type: 'string',
                    default: 'http://localhost:11434'
                },
                maxRetries: {
                    type: 'number',
                    default: 3
                },
                retryDelay: {
                    type: 'number',
                    default: 1000
                },
                timeout: {
                    type: 'number',
                    default: 30000
                }
            }
        });
    }
    get() {
        const config = this.config.store;
        return ConfigSchema.parse(config);
    }
    set(key, value) {
        this.config.set(key, value);
    }
    setAll(config) {
        const validatedConfig = ConfigSchema.partial().parse(config);
        Object.entries(validatedConfig).forEach(([key, value]) => {
            if (value !== undefined) {
                this.config.set(key, value);
            }
        });
    }
    reset() {
        this.config.clear();
    }
    isConfigured() {
        const config = this.get();
        if (config.provider === 'deepseek') {
            return !!(config.apiKey && config.model);
        }
        if (config.provider === 'ollama') {
            return !!(config.ollamaUrl && config.model);
        }
        return false;
    }
    getConfigPath() {
        return this.config.path;
    }
    validate() {
        try {
            const config = this.get();
            const errors = [];
            if (config.provider === 'deepseek' && !config.apiKey) {
                errors.push('Deepseek API key is required');
            }
            if (config.provider === 'ollama' && !config.ollamaUrl) {
                errors.push('Ollama URL is required');
            }
            if (!config.model) {
                errors.push('Model name is required');
            }
            return {
                valid: errors.length === 0,
                errors
            };
        }
        catch (error) {
            return {
                valid: false,
                errors: [`Configuration validation failed: ${error}`]
            };
        }
    }
}
//# sourceMappingURL=settings.js.map