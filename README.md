# 🤖 Arien AI CLI

**Modern AI-powered CLI terminal system with LLM function calling capabilities**

Arien AI CLI is an advanced terminal assistant that leverages the power of Large Language Models (LLMs) with intelligent function calling to help you accomplish complex tasks through natural language interactions. Built with TypeScript 5.8.3 and Node.js 22, it features a "Never Give Up" retry logic system and real-time interface updates.

## 🎯 **Key Features**

### **🧠 Intelligent AI Agent**
- **LLM Providers**: Deepseek (deepseek-chat, deepseek-reasoner) and Ollama support
- **Function Calling**: Advanced tool execution with parallel and sequential strategies
- **Never Give Up Logic**: Automatic retry with exponential backoff for temporary failures
- **Context Awareness**: Maintains conversation history and working directory context

### **🛠️ Powerful Tool Suite**
- **bash**: Execute system commands with full shell access
- **grep**: Fast content search across files with regex support
- **glob**: File pattern matching with modification time sorting
- **write**: Create and overwrite files with directory auto-creation
- **edit**: Intelligent text replacement in existing files
- **web**: Real-time internet search using DuckDuckGo API

### **🎨 Modern User Experience**
- **Real-time Interface**: Streaming responses with progress indicators
- **Custom Animations**: Ball animation with elapsed time tracking `( ●    )` → `(●     )`
- **Interactive Setup**: Guided configuration process
- **Error Handling**: Graceful error recovery and user feedback

## ✨ Features

### 🧠 **Advanced AI Capabilities**
- **LLM Providers**: Deepseek (deepseek-chat, deepseek-reasoner) and Ollama support
- **Intelligent Function Calling**: AI decides when and how to use tools
- **Parallel & Sequential Execution**: Smart tool execution strategies
- **Never Give Up Logic**: Automatic retry with exponential backoff

### 🛠️ **Powerful Tool Suite**
- **🔧 Bash Tool**: Execute commands in interactive shell
- **🔍 Grep Tool**: Fast content search with pattern matching
- **📁 Glob Tool**: File pattern matching and discovery
- **📝 Write Tool**: Create and overwrite files
- **✏️ Edit Tool**: Targeted file modifications
- **🌐 Web Tool**: Real-time internet search via DuckDuckGo

### 🎨 **Modern User Experience**
- **Real-time Interface**: Streaming responses with progress indicators
- **Custom Animations**: Ball animation with elapsed time tracking
- **Interactive Setup**: Guided configuration process
- **Error Handling**: Graceful error recovery and user feedback

## 🚀 Quick Start

### Installation

Use the universal installer script that works on Windows 11 WSL, macOS, and Linux:

```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli

# Run the installer
npm run install-script
```

Or install manually:

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Install globally
npm install -g .
```

### First Run

```bash
# Start the CLI (will guide you through setup)
arien

# Or configure first
arien config
```

### Configuration

On first run, you'll be prompted to configure your LLM provider:

#### Deepseek Setup
1. Get your API key from [Deepseek](https://platform.deepseek.com/)
2. Choose your model: `deepseek-chat` or `deepseek-reasoner`

#### Ollama Setup
1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama3.2`
3. Configure the URL (default: `http://localhost:11434`)

## 📖 Usage Examples

### Basic Commands
```bash
# Start interactive mode
arien

# Show configuration
arien config --show

# Test connection
arien test

# Reset configuration
arien config --reset
```

### AI Interactions
```
You: Create a new TypeScript project with Express
🤖 Arien AI: I'll help you create a new TypeScript project with Express...

You: Find all TODO comments in my code
🤖 Arien AI: I'll search for TODO comments across your codebase...

You: Install the latest version of React
🤖 Arien AI: I'll install the latest React version for you...
```

## 🔧 Tool Descriptions

### Bash Tool
Execute bash commands in an interactive shell environment.
- **Use for**: System commands, file operations, package installation
- **Examples**: `ls -la`, `npm install express`, `git status`

### Grep Tool
Fast content search tool for finding text patterns in files.
- **Use for**: Code search, log analysis, configuration searches
- **Features**: Regex support, recursive search, newest-first sorting

### Glob Tool
File pattern matching tool for finding files by name and pattern.
- **Use for**: File discovery, project structure analysis
- **Patterns**: `**/*.ts`, `**/package.json`, `src/**/*.test.*`

### Write Tool
Create or completely overwrite files with new content.
- **Use for**: New file creation, complete file replacement
- **Features**: Auto-directory creation, encoding support

### Edit Tool
Modify existing files by replacing specific text sections.
- **Use for**: Targeted changes, configuration updates, bug fixes
- **Features**: Global/single replacement, diff preview

### Web Tool
Fetch real-time information from the internet.
- **Use for**: Research, documentation lookup, troubleshooting
- **Features**: DuckDuckGo integration, structured results

## 🏗️ Architecture

```
src/
├── core/
│   ├── agent.ts          # Main AI agent logic
│   ├── llm-providers.ts  # Deepseek & Ollama providers
│   ├── tool-executor.ts  # Tool execution engine
│   └── retry-logic.ts    # Never give up retry system
├── tools/
│   ├── bash.ts          # Bash command execution
│   ├── grep.ts          # Content search tool
│   ├── glob.ts          # File pattern matching
│   ├── write.ts         # File writing tool
│   ├── edit.ts          # File editing tool
│   └── web.ts           # Web search tool
├── ui/
│   ├── interface.ts     # CLI interface
│   ├── animations.ts    # Loading animations
│   └── prompts.ts       # User interaction prompts
├── config/
│   ├── system-prompt.ts # Detailed system prompts
│   └── settings.ts      # Configuration management
└── types/
    └── index.ts         # TypeScript type definitions
```

## 🔄 Never Give Up Logic

The retry system automatically handles:
- **Network Issues**: Connection timeouts, DNS failures
- **Rate Limiting**: Exponential backoff with jitter
- **Temporary Errors**: Service unavailable, server errors
- **Smart Recovery**: Distinguishes temporary vs permanent issues

## 🎯 System Prompt Features

The AI agent includes comprehensive system prompts with:
- **Detailed Tool Descriptions**: When and how to use each tool
- **Execution Strategies**: Parallel vs sequential decision making
- **Best Practices**: Safety guidelines and error handling
- **Examples**: Real-world usage patterns

## 🛡️ Error Handling

- **Graceful Degradation**: Continues operation despite tool failures
- **User Feedback**: Clear error messages and recovery suggestions
- **Retry Logic**: Automatic recovery from temporary issues
- **Safe Operations**: Confirmation prompts for destructive actions

## 📋 Requirements

- **Node.js**: 22.0.0 or higher
- **TypeScript**: 5.8.3
- **Operating System**: Windows 11 WSL, macOS, or Linux
- **LLM Provider**: Deepseek API key or Ollama installation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Deepseek**: For providing advanced reasoning capabilities
- **Ollama**: For local LLM support
- **TypeScript & Node.js**: For the robust development platform
- **Open Source Community**: For the amazing tools and libraries

---

**Built with ❤️ by the Arien AI team**
