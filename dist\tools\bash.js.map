{"version": 3, "file": "bash.js", "sourceRoot": "", "sources": ["../../src/tools/bash.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAE9B,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAQhC,MAAM,OAAO,QAAQ;IACnB,KAAK,CAAC,OAAO,CAAC,MAAsB,EAAE,OAAyB;QAC7D,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC;YACvE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;YAElD,oCAAoC;YACpC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC;YAC5E,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAEvE,sEAAsE;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,EAAE,OAAO,CAAC,EAAE;gBACzD,GAAG,EAAE,UAAU;gBACf,OAAO;gBACP,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE;gBAC/C,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,KAAK,EAAE,qCAAqC;gBACpD,GAAG,EAAE,IAAI,CAAC,iCAAiC;aAC5C,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;YAEtC,OAAO;gBACL,EAAE;gBACF,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iCAAiC,MAAM,CAAC,QAAQ,EAAE;gBAC/E,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACxB,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE;gBACF,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACtC,4BAA4B;QAC5B,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,iDAAiD;QACjD,MAAM,YAAY,GAA8B;YAC9C,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,wCAAwC;YACjD,QAAQ,EAAE,+CAA+C;YACzD,QAAQ,EAAE,+CAA+C;YACzD,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,8BAA8B;YACvC,IAAI,EAAE,aAAa;YACnB,QAAQ,EAAE,6BAA6B;YACvC,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,wBAAwB;YAChC,MAAM,EAAE,eAAe;YACvB,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,cAAc;YACtB,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,oCAAoC;YAC5C,MAAM,EAAE,mCAAmC;YAC3C,OAAO,EAAE,oCAAoC;SAC9C,CAAC;QAEF,gCAAgC;QAChC,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;QAED,wCAAwC;QACxC,IAAI,iBAAiB,GAAG,OAAO,CAAC;QAEhC,oCAAoC;QACpC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAClE,CAAC;QAED,uCAAuC;QACvC,IAAI,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,2BAA2B,CAAC;QACrG,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,MAAW;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC1C,KAAK,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC1C,KAAK,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACxE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC,4BAA4B,CAAC;QAC5G,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,cAAc;QACZ,OAAO;;;;;;;;;;;;;;;;;;;8CAmBmC,CAAC;IAC7C,CAAC;IAED,cAAc,CAAC,MAAW;QACxB,OAAO,CACL,OAAO,MAAM,KAAK,QAAQ;YAC1B,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ;YAClC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;YAChC,CAAC,MAAM,CAAC,gBAAgB,KAAK,SAAS,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,QAAQ,CAAC;YACtF,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CACrE,CAAC;IACJ,CAAC;CACF"}