import { ToolResult } from '../types/index.js';
export interface WebToolParams {
    query: string;
    maxResults?: number;
    timeout?: number;
}
export declare class WebTool {
    private client;
    constructor();
    execute(params: WebToolParams): Promise<ToolResult>;
    private searchWeb;
    private fallbackSearch;
    private extractTitle;
    private formatResults;
    getDescription(): string;
    validateParams(params: any): params is WebToolParams;
    fetchUrl(url: string): Promise<{
        content: string;
        title?: string;
    }>;
}
//# sourceMappingURL=web.d.ts.map