{"version": 3, "file": "interface.d.ts", "sourceRoot": "", "sources": ["../../src/ui/interface.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAe,MAAM,mBAAmB,CAAC;AAExD,qBAAa,aAAa;IACxB,OAAO,CAAC,SAAS,CAAmB;;IAMpC,WAAW,IAAI,IAAI;IAWb,kBAAkB,IAAI,OAAO,CAAC,MAAM,CAAC;YAsB7B,aAAa;YA+Bb,WAAW;IAkCnB,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;IAalC,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAatD,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIrC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,IAAI;IAU1D,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAgB/F,OAAO,CAAC,kBAAkB;IAqB1B,OAAO,CAAC,qBAAqB;IA+B7B,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAI9B,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlC,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAI5B,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlC,aAAa,CAAC,OAAO,SAA+B,GAAG,IAAI;IAI3D,aAAa,CAAC,OAAO,SAAe,GAAG,IAAI;IAI3C,aAAa,IAAI,IAAI;IAIrB,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,SAAK,GAAG,IAAI;IAIhE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAI5D,SAAS,IAAI,IAAI;IAIjB,OAAO,IAAI,IAAI;IAIf,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAgBtC,mBAAmB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAI1C,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAQxC,kBAAkB,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,GAAG,IAAI;IAK1F,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAI5E,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAI/C,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI;IAK9D,qBAAqB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAK1C,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAI9D,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI;IAKhE,iBAAiB,CAAC,KAAK,EAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAWhF,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI;IAO3F,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI;IAShE,gBAAgB,IAAI,IAAI;IAIxB,YAAY,CAAC,KAAK,GAAE,MAAU,GAAG,IAAI;IAIrC,cAAc,CAAC,KAAK,GAAE,MAAU,GAAG,IAAI;IAIvC,mBAAmB,IAAI,IAAI;IAI3B,eAAe,IAAI,IAAI;CAGxB"}