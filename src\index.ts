#!/usr/bin/env node

import { Command } from 'commander';
import { ArienAgent } from './core/agent.js';
import { Settings } from './config/settings.js';
import { UserInterface } from './ui/interface.js';
// import { Config } from './types/index.js';
import chalk from 'chalk';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packagePath = join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(readFileSync(packagePath, 'utf8'));

class ArienCLI {
  private settings: Settings;
  private ui: UserInterface;
  private agent: ArienAgent | null = null;

  constructor() {
    this.settings = new Settings();
    this.ui = new UserInterface();
  }

  async run(): Promise<void> {
    const program = new Command();

    program
      .name('arien')
      .description('Advanced AI Terminal Assistant with LLM Function Calling')
      .version(packageJson.version);

    program
      .command('start')
      .description('Start the interactive AI assistant')
      .option('-c, --config', 'Show configuration setup')
      .action(async (options) => {
        if (options.config) {
          await this.showConfigSetup();
        } else {
          await this.startInteractiveMode();
        }
      });

    program
      .command('config')
      .description('Configure the AI assistant')
      .option('-r, --reset', 'Reset configuration to defaults')
      .option('-s, --show', 'Show current configuration')
      .action(async (options) => {
        if (options.reset) {
          await this.resetConfig();
        } else if (options.show) {
          await this.showConfig();
        } else {
          await this.configureSettings();
        }
      });

    program
      .command('test')
      .description('Test connection to configured LLM provider')
      .action(async () => {
        await this.testConnection();
      });

    // If no command provided, default to interactive mode
    if (process.argv.length <= 2) {
      await this.startInteractiveMode();
      return;
    }

    await program.parseAsync(process.argv);
  }

  private async startInteractiveMode(): Promise<void> {
    try {
      this.ui.showWelcome();

      // Check if configuration exists
      if (!this.settings.isConfigured()) {
        this.ui.showInfo('First time setup required...');
        await this.configureSettings();
      }

      // Validate configuration
      const validation = this.settings.validate();
      if (!validation.valid) {
        this.ui.showError('Configuration is invalid:');
        validation.errors.forEach(error => this.ui.showError(`  - ${error}`));
        await this.configureSettings();
      }

      // Initialize agent
      const config = this.settings.get();
      this.ui.showConfigStatus(config);
      
      this.agent = new ArienAgent(config, this.ui);

      // Test connection
      this.ui.startProgress('Testing connection...');
      const connected = await this.agent.validateConnection();
      this.ui.stopAnimation();

      if (!connected) {
        this.ui.showError('Failed to connect to LLM provider. Please check your configuration.');
        await this.configureSettings();
        return;
      }

      this.ui.showSuccess('Connected successfully! Ready to assist you.');
      this.ui.showInfo('Type your requests and I\'ll help you accomplish them using available tools.');
      this.ui.showInfo('Type "exit" or "quit" to end the session.\n');

      // Main interaction loop
      await this.interactionLoop();

    } catch (error) {
      this.ui.showError(`Failed to start: ${error}`);
      process.exit(1);
    }
  }

  private async interactionLoop(): Promise<void> {
    while (true) {
      try {
        const input = await this.ui.promptUserInput();

        // Handle special commands
        if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
          this.ui.showSuccess('Goodbye! 👋');
          break;
        }

        if (input.toLowerCase() === 'clear') {
          console.clear();
          this.ui.showWelcome();
          continue;
        }

        if (input.toLowerCase() === 'help') {
          this.showHelp();
          continue;
        }

        if (input.toLowerCase().startsWith('cd ')) {
          const newDir = input.substring(3).trim();
          this.agent?.setWorkingDirectory(newDir);
          continue;
        }

        // Process with AI agent
        if (this.agent) {
          await this.agent.processUserInput(input);
        } else {
          this.ui.showError('Agent not initialized. Please restart the application.');
          break;
        }

      } catch (error) {
        this.ui.showError(`Error: ${error}`);
        this.ui.showInfo('You can continue with your next request...');
      }
    }
  }

  private async configureSettings(): Promise<void> {
    try {
      const config = await this.ui.promptInitialSetup();
      this.settings.setAll(config);
      this.ui.showSuccess('Configuration saved successfully!');
    } catch (error) {
      this.ui.showError(`Configuration failed: ${error}`);
      process.exit(1);
    }
  }

  private async showConfigSetup(): Promise<void> {
    const config = this.settings.get();
    this.ui.showConfigStatus(config);
    
    const validation = this.settings.validate();
    if (validation.valid) {
      this.ui.showSuccess('Configuration is valid ✓');
    } else {
      this.ui.showError('Configuration has issues:');
      validation.errors.forEach(error => this.ui.showError(`  - ${error}`));
    }
  }

  private async showConfig(): Promise<void> {
    const config = this.settings.get();
    this.ui.showConfigStatus(config);
    console.log(chalk.gray(`Config file: ${this.settings.getConfigPath()}`));
  }

  private async resetConfig(): Promise<void> {
    const confirm = await this.ui.confirmAction('This will reset all configuration. Continue?');
    if (confirm) {
      this.settings.reset();
      this.ui.showSuccess('Configuration reset successfully!');
    }
  }

  private async testConnection(): Promise<void> {
    try {
      if (!this.settings.isConfigured()) {
        this.ui.showError('Please configure the application first using: arien config');
        return;
      }

      const config = this.settings.get();
      this.ui.showInfo(`Testing connection to ${config.provider}...`);
      
      const agent = new ArienAgent(config, this.ui);
      
      this.ui.startProgress('Connecting...');
      const connected = await agent.validateConnection();
      this.ui.stopAnimation();

      if (connected) {
        this.ui.showSuccess('Connection successful! ✓');
      } else {
        this.ui.showError('Connection failed! ✗');
      }

    } catch (error) {
      this.ui.stopAnimation();
      this.ui.showError(`Connection test failed: ${error}`);
    }
  }

  private showHelp(): void {
    console.log(chalk.cyan.bold('\n🤖 Arien AI CLI Help\n'));
    
    console.log(chalk.yellow('Available Commands:'));
    console.log('  help     - Show this help message');
    console.log('  clear    - Clear the screen');
    console.log('  cd <dir> - Change working directory');
    console.log('  exit     - Exit the application');
    console.log('  quit     - Exit the application\n');
    
    console.log(chalk.yellow('Tool Capabilities:'));
    console.log('  🔧 bash  - Execute bash commands');
    console.log('  🔍 grep  - Search file contents');
    console.log('  📁 glob  - Find files by pattern');
    console.log('  📝 write - Create/overwrite files');
    console.log('  ✏️  edit  - Modify existing files');
    console.log('  🌐 web   - Search the internet\n');
    
    console.log(chalk.yellow('Examples:'));
    console.log('  "Create a new TypeScript project"');
    console.log('  "Find all TODO comments in my code"');
    console.log('  "Install the latest version of express"');
    console.log('  "Search for Node.js best practices"');
    console.log('  "Fix the syntax error in app.ts"\n');
  }
}

// Handle uncaught errors gracefully
process.on('uncaughtException', (error) => {
  console.error(chalk.red('\n💥 Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('\n💥 Unhandled Rejection:'), reason);
  process.exit(1);
});

// Main execution
const cli = new ArienCLI();
cli.run().catch((error) => {
  console.error(chalk.red('\n💥 Fatal Error:'), error);
  process.exit(1);
});
