{"version": 3, "file": "tool-executor.js", "sourceRoot": "", "sources": ["../../src/core/tool-executor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAoD,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACjG,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAEhD,MAAM,OAAO,YAAY;IACf,QAAQ,CAAW;IACnB,QAAQ,CAAW;IACnB,QAAQ,CAAW;IACnB,SAAS,CAAY;IACrB,QAAQ,CAAW;IACnB,OAAO,CAAU;IACjB,YAAY,CAAe;IAEnC;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,QAAkB,EAAE,OAAyB;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,UAAU,CAClB,gCAAgC,QAAQ,CAAC,IAAI,EAAE,EAC/C,qBAAqB,CACtB,CAAC;YACJ,CAAC;YAED,mEAAmE;YACnE,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;gBAC5D,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACtB,KAAK,MAAM;wBACT,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC;oBAE1E,KAAK,MAAM;wBACT,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,CAAC,CAAC;oBAEjE,KAAK,MAAM;wBACT,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,CAAC,CAAC;oBAEjE,KAAK,OAAO;wBACV,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,CAAC,CAAC;oBAElE,KAAK,MAAM;wBACT,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,CAAC,CAAC;oBAEjE,KAAK,KAAK;wBACR,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAiB,CAAC,CAAC;oBAEhE;wBACE,MAAM,IAAI,UAAU,CAClB,sBAAsB,QAAQ,CAAC,IAAI,EAAE,EACrC,mBAAmB,CACpB,CAAC;gBACN,CAAC;YACH,CAAC,EAAE,QAAQ,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,EAAE;gBACpD,qDAAqD;gBACrD,OAAO,CAAC,KAAK,CAAC,QAAQ,QAAQ,CAAC,IAAI,kBAAkB,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC7C,uDAAuD;QACvD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,OAAO;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,CAAC;iBACnB,CAAC;YACJ,KAAK,MAAM;gBACT,OAAO;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,GAAG;iBACrB,CAAC;YACJ,KAAK,MAAM,CAAC;YACZ,KAAK,MAAM;gBACT,OAAO;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,GAAG;oBACd,eAAe,EAAE,CAAC;iBACnB,CAAC;YACJ,KAAK,OAAO,CAAC;YACb,KAAK,MAAM;gBACT,OAAO;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,CAAC;iBACnB,CAAC;YACJ;gBACE,OAAO;oBACL,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,CAAC;iBACnB,CAAC;QACN,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAqB,EAAE,OAAyB;QACzE,iFAAiF;QACjF,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,sDAAsD;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACrD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAChD,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;YACjF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAE9B,6DAA6D;YAC7D,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,yBAAyB,CAAC,SAAqB;QACrD,wEAAwE;QACxE,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC/C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAC3C,CAAC,MAAM,CAAC;QAET,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAC3C,CAAC,MAAM,CAAC;QAET,6CAA6C;QAC7C,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,kCAAkC;QAC1E,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iCAAiC;QACzE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAqB,EAAE,OAAyB;QAC3E,qFAAqF;QACrF,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,QAAQ;gBAAE,SAAS,CAAC,yBAAyB;YAElD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,mDAAmD;YACnD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACvD,QAAQ,EACR,MAAM,EACN,CAAC,EACD,SAAS,CAAC,MAAM,EAChB,OAAO,CACR,CAAC;gBAEF,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,MAAM;gBACR,CAAC;YACH,CAAC;YAED,oDAAoD;YACpD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,UAAoB,EACpB,MAAkB,EAClB,aAAqB,EACrB,WAAmB,EACnB,OAAyB;QAEzB,+DAA+D;QAE/D,+CAA+C;QAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iEAAiE;QACjE,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3C,sCAAsC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAClE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,wDAAwD;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,uBAAuB,CAAC,QAAkB,EAAE,OAAmB,EAAE,OAAyB;QAChG,4DAA4D;QAC5D,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,MAAM;gBACT,kDAAkD;gBAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAW,CAAC;gBACzD,IAAI,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,kDAAkD;oBAClD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAClD,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC1B,4FAA4F;wBAC5F,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACxC,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,OAAO,CAAC;YACb,KAAK,MAAM;gBACT,6DAA6D;gBAC7D,MAAM;YAER;gBACE,4CAA4C;gBAC5C,MAAM;QACV,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,UAAoB,EAAE,OAAyB;QAC3E,oEAAoE;QAEpE,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;YACxB,KAAK,MAAM;gBACT,kDAAkD;gBAClD,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEnE,KAAK,KAAK;gBACR,sDAAsD;gBACtD,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAExD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,SAAmB,EAAE,QAA0B;QACrF,gDAAgD;QAChD,8DAA8D;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAAmB;QACvD,8CAA8C;QAC9C,8DAA8D;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,mBAAmB,CAAC,KAAc;QACxC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAEzB,MAAM,oBAAoB,GAAG;YAC3B,mBAAmB;YACnB,2BAA2B;YAC3B,mBAAmB;YACnB,qBAAqB;YACrB,oBAAoB;SACrB,CAAC;QAEF,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAC3C,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CACxC,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACzC,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE3D,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE3D,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE3D,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE5D,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE3D,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE1D;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAkB;QACvC,uEAAuE;QACvE,MAAM,aAAa,GAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC5D,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,kBAAkB,CAAC,QAAkB;QACnC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAExC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAExC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAExC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YAEzC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAExC,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAEvC;gBACE,OAAO,cAAc,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,sBAAsB;QACpB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACpC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACpC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACpC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;YACtC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACpC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;SACnC,CAAC;IACJ,CAAC;IAED,iDAAiD;IACjD,0BAA0B,CAAC,SAAqB;QAC9C,mEAAmE;QACnE,MAAM,kBAAkB,GAAe,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACjE,MAAM,mBAAmB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE3F,gFAAgF;QAChF,MAAM,YAAY,GAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/F,8DAA8D;QAC9D,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;QAErE,IAAI,mBAAmB,IAAI,gBAAgB,IAAI,eAAe,EAAE,CAAC;YAC/D,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,4CAA4C;IAC5C,qBAAqB,CAAC,SAAqB;QACzC,MAAM,aAAa,GAA6B;YAC9C,IAAI,EAAE,IAAI,EAAK,8BAA8B;YAC7C,IAAI,EAAE,IAAI,EAAK,4BAA4B;YAC3C,IAAI,EAAE,IAAI,EAAK,qCAAqC;YACpD,KAAK,EAAE,GAAG,EAAK,+BAA+B;YAC9C,IAAI,EAAE,IAAI,EAAK,4BAA4B;YAC3C,GAAG,EAAE,KAAK,CAAK,8BAA8B;SAC9C,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5B,iDAAiD;YACjD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;CACF"}