import chalk from 'chalk';
export class AnimationManager {
    currentAnimation = null;
    frameIndex = 0;
    startTime = Date.now();
    // Custom ball animation with elapsed seconds
    ballFrames = [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )",
    ];
    spinnerFrames = [
        "⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"
    ];
    dotsFrames = [
        "⠋", "⠙", "⠚", "⠞", "⠖", "⠦", "⠴", "⠲", "⠳", "⠓"
    ];
    thinkingFrames = [
        "🤔", "💭", "🧠", "⚡", "💡"
    ];
    start(type = 'ball', message = '') {
        this.stop();
        this.frameIndex = 0;
        this.startTime = Date.now();
        const frames = this.getFrames(type);
        this.currentAnimation = setInterval(() => {
            const frame = frames[this.frameIndex % frames.length];
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            let display = '';
            if (type === 'ball') {
                display = `${chalk.cyan(frame)} ${chalk.gray(`${elapsed}s`)} ${message}`;
            }
            else if (type === 'thinking') {
                display = `${frame} ${chalk.yellow('Thinking...')} ${chalk.gray(`${elapsed}s`)} ${message}`;
            }
            else {
                display = `${chalk.cyan(frame)} ${message} ${chalk.gray(`${elapsed}s`)}`;
            }
            process.stdout.write(`\r${display}`);
            this.frameIndex++;
        }, 100);
    }
    stop() {
        if (this.currentAnimation) {
            clearInterval(this.currentAnimation);
            this.currentAnimation = null;
            process.stdout.write('\r');
        }
    }
    getFrames(type) {
        switch (type) {
            case 'ball':
                return this.ballFrames;
            case 'spinner':
                return this.spinnerFrames;
            case 'dots':
                return this.dotsFrames;
            case 'thinking':
                return this.thinkingFrames;
            default:
                return this.ballFrames;
        }
    }
    showProgress(current, total, message = '') {
        const percentage = Math.round((current / total) * 100);
        const filled = Math.round((current / total) * 20);
        const empty = 20 - filled;
        const progressBar = '█'.repeat(filled) + '░'.repeat(empty);
        const display = `${chalk.cyan(progressBar)} ${percentage}% ${message}`;
        process.stdout.write(`\r${display}`);
        if (current === total) {
            process.stdout.write('\n');
        }
    }
    showSuccess(message) {
        console.log(`${chalk.green('✓')} ${message}`);
    }
    showError(message) {
        console.log(`${chalk.red('✗')} ${message}`);
    }
    showWarning(message) {
        console.log(`${chalk.yellow('⚠')} ${message}`);
    }
    showInfo(message) {
        console.log(`${chalk.blue('ℹ')} ${message}`);
    }
    showStep(step, total, message) {
        console.log(`${chalk.cyan(`[${step}/${total}]`)} ${message}`);
    }
    clearLine() {
        process.stdout.write('\r\x1b[K');
    }
    newLine() {
        console.log();
    }
}
//# sourceMappingURL=animations.js.map