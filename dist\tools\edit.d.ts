import { ToolResult } from '../types/index.js';
export interface EditToolParams {
    path: string;
    search: string;
    replace: string;
    global?: boolean;
    createIfNotExists?: boolean;
}
export declare class EditTool {
    execute(params: EditToolParams): Promise<ToolResult>;
    private editFile;
    private performReplacement;
    private escapeRegExp;
    private fileExists;
    getDescription(): string;
    validateParams(params: any): params is EditToolParams;
    previewEdit(params: EditToolParams): Promise<{
        preview: string;
        replacements: number;
    }>;
    showDiff(params: EditToolParams): Promise<string>;
}
//# sourceMappingURL=edit.d.ts.map