export declare class AnimationManager {
    private currentAnimation;
    private frameIndex;
    private startTime;
    private readonly ballFrames;
    private readonly spinnerFrames;
    private readonly dotsFrames;
    private readonly thinkingFrames;
    start(type?: 'ball' | 'spinner' | 'dots' | 'thinking', message?: string): void;
    stop(): void;
    private getFrames;
    showProgress(current: number, total: number, message?: string): void;
    showSuccess(message: string): void;
    showError(message: string): void;
    showWarning(message: string): void;
    showInfo(message: string): void;
    showStep(step: number, total: number, message: string): void;
    clearLine(): void;
    newLine(): void;
}
//# sourceMappingURL=animations.d.ts.map