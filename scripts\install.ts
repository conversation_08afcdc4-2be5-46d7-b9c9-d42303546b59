#!/usr/bin/env node

import { execSync } from 'child_process';
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import inquirer from 'inquirer';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

interface InstallOptions {
  action: 'install' | 'update' | 'uninstall';
  global: boolean;
  force: boolean;
}

class ArienInstaller {
  private platform: string;
  private isWindows: boolean;
  private isWSL: boolean;

  constructor() {
    this.platform = process.platform;
    this.isWindows = this.platform === 'win32';
    this.isWSL = this.detectWSL();
  }

  async run(): Promise<void> {
    try {
      console.log(chalk.cyan.bold(`
╔═══════════════════════════════════════╗
║        🤖 ARIEN AI CLI INSTALLER      ║
║     Universal Installation Script     ║
╚═══════════════════════════════════════╝
      `));

      this.showSystemInfo();
      
      const options = await this.promptInstallOptions();
      
      switch (options.action) {
        case 'install':
          await this.install(options);
          break;
        case 'update':
          await this.update(options);
          break;
        case 'uninstall':
          await this.uninstall(options);
          break;
      }

    } catch (error) {
      console.error(chalk.red('\n💥 Installation failed:'), error);
      process.exit(1);
    }
  }

  private detectWSL(): boolean {
    try {
      const release = execSync('uname -r', { encoding: 'utf8' });
      return release.toLowerCase().includes('microsoft') || release.toLowerCase().includes('wsl');
    } catch {
      return false;
    }
  }

  private showSystemInfo(): void {
    console.log(chalk.blue('📋 System Information:'));
    console.log(`   Platform: ${chalk.cyan(this.platform)}`);
    console.log(`   Node.js: ${chalk.cyan(process.version)}`);
    console.log(`   Architecture: ${chalk.cyan(process.arch)}`);

    if (this.isWSL) {
      console.log(`   Environment: ${chalk.cyan('Windows Subsystem for Linux (WSL)')}`);
    } else if (this.isWindows) {
      console.log(`   Environment: ${chalk.cyan('Windows')}`);
    } else {
      console.log(`   Environment: ${chalk.cyan(this.platform === 'darwin' ? 'macOS' : 'Linux')}`);
    }

    // Show additional system information
    console.log(`   Shell: ${chalk.cyan(process.env.SHELL || 'Unknown')}`);
    console.log(`   Home Directory: ${chalk.cyan(process.env.HOME || process.env.USERPROFILE || 'Unknown')}`);

    console.log();
  }

  private async promptInstallOptions(): Promise<InstallOptions> {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: '📦 Install Arien AI CLI', value: 'install' },
          { name: '🔄 Update existing installation', value: 'update' },
          { name: '🗑️  Uninstall Arien AI CLI', value: 'uninstall' }
        ]
      },
      {
        type: 'confirm',
        name: 'global',
        message: 'Install globally (recommended)?',
        default: true,
        when: (answers) => answers.action === 'install' || answers.action === 'update'
      },
      {
        type: 'confirm',
        name: 'force',
        message: 'Force installation (overwrite existing)?',
        default: false,
        when: (answers) => answers.action === 'install'
      }
    ]);

    return answers as InstallOptions;
  }

  private async install(options: InstallOptions): Promise<void> {
    console.log(chalk.yellow('\n🚀 Starting installation...\n'));

    // Step 1: Check Node.js version
    await this.checkNodeVersion();

    // Step 2: Check if already installed
    if (!options.force) {
      await this.checkExistingInstallation();
    }

    // Step 3: Install dependencies
    await this.installDependencies();

    // Step 4: Build the project
    await this.buildProject();

    // Step 5: Install globally or locally
    if (options.global) {
      await this.installGlobally();
    } else {
      await this.installLocally();
    }

    // Step 6: Verify installation
    await this.verifyInstallation();

    console.log(chalk.green.bold('\n✅ Installation completed successfully!\n'));
    this.showUsageInstructions();
  }

  private async update(options: InstallOptions): Promise<void> {
    console.log(chalk.yellow('\n🔄 Updating Arien AI CLI...\n'));

    // Check if installed
    const isInstalled = await this.checkIfInstalled();
    if (!isInstalled) {
      console.log(chalk.yellow('Arien AI CLI is not installed. Installing instead...'));
      await this.install({ ...options, action: 'install' });
      return;
    }

    // Update dependencies
    await this.installDependencies();

    // Rebuild
    await this.buildProject();

    // Reinstall
    if (options.global) {
      await this.installGlobally();
    } else {
      await this.installLocally();
    }

    console.log(chalk.green.bold('\n✅ Update completed successfully!\n'));
  }

  private async uninstall(_options: InstallOptions): Promise<void> {
    console.log(chalk.yellow('\n🗑️  Uninstalling Arien AI CLI...\n'));

    const confirm = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmed',
        message: 'Are you sure you want to uninstall Arien AI CLI?',
        default: false
      }
    ]);

    if (!confirm.confirmed) {
      console.log(chalk.gray('Uninstallation cancelled.'));
      return;
    }

    try {
      // Uninstall globally
      console.log(chalk.blue('📦 Removing global package...'));
      execSync('npm uninstall -g arien-ai-cli', { stdio: 'inherit' });
      
      console.log(chalk.green.bold('\n✅ Uninstallation completed successfully!\n'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Global uninstall failed or package not found globally.'));
    }
  }

  private async checkNodeVersion(): Promise<void> {
    console.log(chalk.blue('🔍 Checking Node.js version...'));
    
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 22) {
      throw new Error(`Node.js 22 or higher is required. Current version: ${nodeVersion}`);
    }
    
    console.log(chalk.green(`✓ Node.js ${nodeVersion} is compatible`));
  }

  private async checkExistingInstallation(): Promise<void> {
    console.log(chalk.blue('🔍 Checking for existing installation...'));
    
    const isInstalled = await this.checkIfInstalled();
    if (isInstalled) {
      const overwrite = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: 'Arien AI CLI is already installed. Overwrite?',
          default: false
        }
      ]);

      if (!overwrite.overwrite) {
        console.log(chalk.gray('Installation cancelled.'));
        process.exit(0);
      }
    }
  }

  private async checkIfInstalled(): Promise<boolean> {
    try {
      execSync('arien --version', { stdio: 'pipe' });
      return true;
    } catch {
      return false;
    }
  }

  private async installDependencies(): Promise<void> {
    console.log(chalk.blue('📦 Installing dependencies...'));
    
    process.chdir(projectRoot);
    
    // Detect package manager
    const packageManager = await this.detectPackageManager();
    console.log(chalk.gray(`Using package manager: ${packageManager}`));
    
    const installCommand = packageManager === 'yarn' ? 'yarn install' : 
                          packageManager === 'pnpm' ? 'pnpm install' : 'npm install';
    
    execSync(installCommand, { stdio: 'inherit' });
    console.log(chalk.green('✓ Dependencies installed'));
  }

  private async detectPackageManager(): Promise<string> {
    try {
      await fs.access(join(projectRoot, 'yarn.lock'));
      return 'yarn';
    } catch {}

    try {
      await fs.access(join(projectRoot, 'pnpm-lock.yaml'));
      return 'pnpm';
    } catch {}

    return 'npm';
  }

  private async buildProject(): Promise<void> {
    console.log(chalk.blue('🔨 Building project...'));
    
    execSync('npm run build', { stdio: 'inherit', cwd: projectRoot });
    console.log(chalk.green('✓ Project built successfully'));
  }

  private async installGlobally(): Promise<void> {
    console.log(chalk.blue('🌍 Installing globally...'));
    
    execSync('npm install -g .', { stdio: 'inherit', cwd: projectRoot });
    console.log(chalk.green('✓ Installed globally'));
  }

  private async installLocally(): Promise<void> {
    console.log(chalk.blue('📁 Installing locally...'));
    
    execSync('npm link', { stdio: 'inherit', cwd: projectRoot });
    console.log(chalk.green('✓ Linked locally'));
  }

  private async verifyInstallation(): Promise<void> {
    console.log(chalk.blue('🔍 Verifying installation...'));
    
    try {
      const output = execSync('arien --version', { encoding: 'utf8' });
      console.log(chalk.green(`✓ Arien AI CLI ${output.trim()} is ready`));
    } catch (error) {
      throw new Error('Installation verification failed. The "arien" command is not available.');
    }
  }

  private showUsageInstructions(): void {
    console.log(chalk.cyan.bold('🎉 Getting Started:\n'));
    console.log(chalk.white('1. Run the CLI:'));
    console.log(chalk.gray('   arien\n'));
    console.log(chalk.white('2. Configure your LLM provider:'));
    console.log(chalk.gray('   arien config\n'));
    console.log(chalk.white('3. Test the connection:'));
    console.log(chalk.gray('   arien test\n'));
    console.log(chalk.white('4. Get help:'));
    console.log(chalk.gray('   arien --help\n'));

    console.log(chalk.yellow('💡 First-time Setup:'));
    console.log(chalk.gray('   • The first time you run "arien", you\'ll be guided through setup'));
    console.log(chalk.gray('   • Choose between Deepseek (cloud) or Ollama (local) providers'));
    console.log(chalk.gray('   • For Deepseek: Get your API key from https://platform.deepseek.com'));
    console.log(chalk.gray('   • For Ollama: Make sure Ollama is installed and running locally\n'));

    console.log(chalk.green('🚀 Example Commands:'));
    console.log(chalk.gray('   "Create a new TypeScript project"'));
    console.log(chalk.gray('   "Find all TODO comments in my code"'));
    console.log(chalk.gray('   "Install the latest version of express"'));
    console.log(chalk.gray('   "Search for Node.js best practices"'));
    console.log(chalk.gray('   "Fix the syntax error in app.ts"\n'));

    console.log(chalk.blue('📚 Documentation:'));
    console.log(chalk.gray('   • GitHub: https://github.com/arien-ai/arien-ai-cli'));
    console.log(chalk.gray('   • Issues: https://github.com/arien-ai/arien-ai-cli/issues'));
    console.log(chalk.gray('   • Discussions: https://github.com/arien-ai/arien-ai-cli/discussions\n'));
  }
}

// Main execution
const installer = new ArienInstaller();
installer.run().catch((error) => {
  console.error(chalk.red('\n💥 Installation failed:'), error);
  process.exit(1);
});
