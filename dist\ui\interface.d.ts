import { Config } from '../types/index.js';
export declare class UserInterface {
    private animation;
    constructor();
    showWelcome(): void;
    promptInitialSetup(): Promise<Config>;
    private setupDeepseek;
    private setupOllama;
    promptUserInput(): Promise<string>;
    confirmAction(message: string): Promise<boolean>;
    showAIResponse(content: string): void;
    showToolExecution(toolType: string, parameters: any): void;
    showToolResult(toolType: string, success: boolean, output: string, executionTime: number): void;
    showError(error: string): void;
    showWarning(warning: string): void;
    showInfo(info: string): void;
    showSuccess(message: string): void;
    startThinking(message?: string): void;
    startProgress(message?: string): void;
    stopAnimation(): void;
    showProgress(current: number, total: number, message?: string): void;
    showStep(step: number, total: number, message: string): void;
    clearLine(): void;
    newLine(): void;
    showConfigStatus(config: Config): void;
}
//# sourceMappingURL=interface.d.ts.map