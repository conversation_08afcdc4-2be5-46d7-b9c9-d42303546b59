import { Config } from '../types/index.js';
export declare class UserInterface {
    private animation;
    constructor();
    showWelcome(): void;
    promptInitialSetup(): Promise<Config>;
    private setupDeepseek;
    private setupOllama;
    promptUserInput(): Promise<string>;
    confirmAction(message: string): Promise<boolean>;
    showAIResponse(content: string): void;
    showToolExecution(toolType: string, parameters: any): void;
    showToolResult(toolType: string, success: boolean, output: string, executionTime: number): void;
    private sanitizeParameters;
    private generateOutputSummary;
    showError(error: string): void;
    showWarning(warning: string): void;
    showInfo(info: string): void;
    showSuccess(message: string): void;
    startThinking(message?: string): void;
    startProgress(message?: string): void;
    stopAnimation(): void;
    showProgress(current: number, total: number, message?: string): void;
    showStep(step: number, total: number, message: string): void;
    clearLine(): void;
    newLine(): void;
    showConfigStatus(config: Config): void;
    showThinkingProcess(message: string): void;
    showPlanningPhase(steps: string[]): void;
    showExecutionPhase(currentStep: number, totalSteps: number, stepDescription: string): void;
    showRetryAttempt(attempt: number, maxAttempts: number, reason: string): void;
    showAlternativeStrategy(strategy: string): void;
    showProgressUpdate(message: string, percentage?: number): void;
    showStreamingResponse(chunk: string): void;
    showToolOutputSummary(toolType: string, summary: string): void;
    showDecisionRationale(decision: string, reasoning: string): void;
    showResourceUsage(stats: {
        memory?: number;
        cpu?: number;
        time?: number;
    }): void;
    showCompletionSummary(totalSteps: number, successfulSteps: number, totalTime: number): void;
    showInteractivePrompt(message: string, options?: string[]): void;
    clearCurrentLine(): void;
    moveCursorUp(lines?: number): void;
    moveCursorDown(lines?: number): void;
    saveCurrentPosition(): void;
    restorePosition(): void;
}
//# sourceMappingURL=interface.d.ts.map