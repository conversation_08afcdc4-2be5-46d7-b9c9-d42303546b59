import { promises as fs } from 'fs';
import { join } from 'path';
import { nanoid } from 'nanoid';
export class GrepTool {
    async execute(params) {
        const id = nanoid();
        const startTime = Date.now();
        try {
            const results = await this.searchFiles(params);
            const output = this.formatResults(results, params.pattern);
            return {
                id,
                success: true,
                output,
                executionTime: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                executionTime: Date.now() - startTime
            };
        }
    }
    async searchFiles(params) {
        const results = [];
        const maxResults = params.maxResults || 100;
        const flags = params.ignoreCase ? 'gi' : 'g';
        const regex = new RegExp(params.pattern, flags);
        try {
            const stat = await fs.stat(params.path);
            if (stat.isFile()) {
                const fileResults = await this.searchInFile(params.path, regex);
                results.push(...fileResults);
            }
            else if (stat.isDirectory()) {
                const dirResults = await this.searchInDirectory(params.path, regex, params.recursive || true);
                results.push(...dirResults);
            }
        }
        catch (error) {
            throw new Error(`Cannot access path: ${params.path}`);
        }
        // Sort by modification time (newest first) and limit results
        results.sort((a, b) => b.modifiedTime - a.modifiedTime);
        return results.slice(0, maxResults);
    }
    async searchInFile(filePath, regex) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const lines = content.split('\n');
            const matches = [];
            const stat = await fs.stat(filePath);
            lines.forEach((line, index) => {
                const match = line.match(regex);
                if (match) {
                    matches.push({
                        filePath,
                        lineNumber: index + 1,
                        line: line.trim(),
                        match: match[0],
                        modifiedTime: stat.mtime.getTime()
                    });
                }
            });
            return matches;
        }
        catch (error) {
            // Skip files that can't be read (binary files, permission issues, etc.)
            return [];
        }
    }
    async searchInDirectory(dirPath, regex, recursive) {
        const results = [];
        try {
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = join(dirPath, entry.name);
                if (entry.isFile()) {
                    // Skip binary files and common non-text files
                    if (this.isTextFile(entry.name)) {
                        const fileResults = await this.searchInFile(fullPath, regex);
                        results.push(...fileResults);
                    }
                }
                else if (entry.isDirectory() && recursive) {
                    // Skip common directories that shouldn't be searched
                    if (!this.shouldSkipDirectory(entry.name)) {
                        const dirResults = await this.searchInDirectory(fullPath, regex, recursive);
                        results.push(...dirResults);
                    }
                }
            }
        }
        catch (error) {
            // Skip directories that can't be read
        }
        return results;
    }
    isTextFile(filename) {
        const textExtensions = [
            '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.json', '.xml', '.html', '.htm',
            '.css', '.scss', '.sass', '.less', '.py', '.java', '.c', '.cpp', '.h', '.hpp',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.sh', '.bash',
            '.zsh', '.fish', '.ps1', '.bat', '.cmd', '.yml', '.yaml', '.toml', '.ini',
            '.cfg', '.conf', '.config', '.env', '.gitignore', '.gitattributes', '.editorconfig',
            '.eslintrc', '.prettierrc', '.babelrc', '.tsconfig', '.dockerfile', '.makefile'
        ];
        const ext = filename.toLowerCase();
        return textExtensions.some(textExt => ext.endsWith(textExt)) ||
            !filename.includes('.'); // Files without extensions are often text
    }
    shouldSkipDirectory(dirname) {
        const skipDirs = [
            'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'target',
            '.next', '.nuxt', 'coverage', '.nyc_output', 'tmp', 'temp',
            '.cache', '.vscode', '.idea', '__pycache__', '.pytest_cache'
        ];
        return skipDirs.includes(dirname) || dirname.startsWith('.');
    }
    formatResults(results, pattern) {
        if (results.length === 0) {
            return `No matches found for pattern: ${pattern}`;
        }
        const groupedByFile = this.groupByFile(results);
        const output = [];
        output.push(`Found ${results.length} matches in ${Object.keys(groupedByFile).length} files for pattern: ${pattern}\n`);
        Object.entries(groupedByFile).forEach(([filePath, matches]) => {
            output.push(`📄 ${filePath} (${matches.length} matches)`);
            matches.slice(0, 10).forEach(match => {
                output.push(`   ${match.lineNumber}: ${match.line}`);
            });
            if (matches.length > 10) {
                output.push(`   ... and ${matches.length - 10} more matches`);
            }
            output.push('');
        });
        return output.join('\n');
    }
    groupByFile(results) {
        return results.reduce((groups, result) => {
            if (!groups[result.filePath]) {
                groups[result.filePath] = [];
            }
            groups[result.filePath].push(result);
            return groups;
        }, {});
    }
    getDescription() {
        return `Fast content search tool that finds files containing specific text or patterns.
    
Usage:
- Finding files containing specific text or patterns
- Code search and analysis
- Log file analysis
- Configuration file searches

Examples:
- Search for "function" in all .js files
- Find configuration values in config files
- Locate error messages in log files

Notes:
- Returns matching file paths sorted by modification time (newest first)
- Supports regex patterns and case-insensitive search
- Automatically skips binary files and common build directories
- Limits results to prevent overwhelming output`;
    }
    validateParams(params) {
        return (typeof params === 'object' &&
            typeof params.pattern === 'string' &&
            params.pattern.trim().length > 0 &&
            typeof params.path === 'string' &&
            params.path.trim().length > 0 &&
            (params.recursive === undefined || typeof params.recursive === 'boolean') &&
            (params.ignoreCase === undefined || typeof params.ignoreCase === 'boolean') &&
            (params.maxResults === undefined || typeof params.maxResults === 'number'));
    }
}
//# sourceMappingURL=grep.js.map