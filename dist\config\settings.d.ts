import { Config } from '../types/index.js';
export declare class Settings {
    private config;
    constructor();
    get(): Config;
    set(key: keyof Config, value: any): void;
    setAll(config: Partial<Config>): void;
    reset(): void;
    isConfigured(): boolean;
    getConfigPath(): string;
    validate(): {
        valid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=settings.d.ts.map