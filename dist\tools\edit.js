import { promises as fs } from 'fs';
import { nanoid } from 'nanoid';
export class EditTool {
    async execute(params) {
        const id = nanoid();
        const startTime = Date.now();
        try {
            const result = await this.editFile(params);
            return {
                id,
                success: true,
                output: result.message,
                executionTime: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                executionTime: Date.now() - startTime
            };
        }
    }
    async editFile(params) {
        try {
            // Check if file exists
            const fileExists = await this.fileExists(params.path);
            if (!fileExists) {
                if (params.createIfNotExists) {
                    // Create new file with the replacement content
                    await fs.writeFile(params.path, params.replace, 'utf8');
                    return {
                        message: `Created new file: ${params.path} with content`,
                        replacements: 1
                    };
                }
                else {
                    throw new Error(`File does not exist: ${params.path}`);
                }
            }
            // Read the file
            const content = await fs.readFile(params.path, 'utf8');
            // Perform the replacement
            const { newContent, replacements } = this.performReplacement(content, params);
            if (replacements === 0) {
                return {
                    message: `No matches found for search text in ${params.path}`,
                    replacements: 0
                };
            }
            // Write the modified content back
            await fs.writeFile(params.path, newContent, 'utf8');
            const message = `Successfully made ${replacements} replacement${replacements > 1 ? 's' : ''} in ${params.path}`;
            return { message, replacements };
        }
        catch (error) {
            if (error instanceof Error) {
                if (error.message.includes('ENOENT')) {
                    throw new Error(`File not found: ${params.path}`);
                }
                else if (error.message.includes('EACCES')) {
                    throw new Error(`Permission denied: ${params.path}`);
                }
                else if (error.message.includes('EISDIR')) {
                    throw new Error(`Path is a directory, not a file: ${params.path}`);
                }
            }
            throw error;
        }
    }
    performReplacement(content, params) {
        let newContent;
        let replacements = 0;
        if (params.global) {
            // Global replacement - replace all occurrences
            const regex = new RegExp(this.escapeRegExp(params.search), 'g');
            newContent = content.replace(regex, () => {
                replacements++;
                return params.replace;
            });
        }
        else {
            // Single replacement - replace only the first occurrence
            const index = content.indexOf(params.search);
            if (index !== -1) {
                newContent = content.substring(0, index) +
                    params.replace +
                    content.substring(index + params.search.length);
                replacements = 1;
            }
            else {
                newContent = content;
                replacements = 0;
            }
        }
        return { newContent, replacements };
    }
    escapeRegExp(string) {
        // Escape special regex characters
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    async fileExists(path) {
        try {
            await fs.access(path);
            return true;
        }
        catch {
            return false;
        }
    }
    getDescription() {
        return `Edit existing files by replacing text sections, creating new files, or deleting content.
    
Usage:
- Making targeted changes to existing files
- Updating configuration values
- Fixing bugs or adding features to code
- Appending content to files

Examples:
- Replace function implementations
- Update configuration values
- Add new imports or dependencies
- Fix syntax errors

Features:
- Supports both single and global replacements
- Can create new files if they don't exist
- Preserves file encoding and line endings
- Provides detailed feedback on changes made

Important Notes:
- Always specify exact text to replace
- Be careful with whitespace and formatting
- For large edits, consider using write tool to overwrite
- For moving/renaming files, use bash tool with mv command`;
    }
    validateParams(params) {
        return (typeof params === 'object' &&
            typeof params.path === 'string' &&
            params.path.trim().length > 0 &&
            typeof params.search === 'string' &&
            typeof params.replace === 'string' &&
            (params.global === undefined || typeof params.global === 'boolean') &&
            (params.createIfNotExists === undefined || typeof params.createIfNotExists === 'boolean'));
    }
    // Utility method to preview changes without applying them
    async previewEdit(params) {
        try {
            const fileExists = await this.fileExists(params.path);
            if (!fileExists) {
                if (params.createIfNotExists) {
                    return {
                        preview: `Would create new file with content:\n${params.replace}`,
                        replacements: 1
                    };
                }
                else {
                    throw new Error(`File does not exist: ${params.path}`);
                }
            }
            const content = await fs.readFile(params.path, 'utf8');
            const { newContent, replacements } = this.performReplacement(content, params);
            return { preview: newContent, replacements };
        }
        catch (error) {
            throw error;
        }
    }
    // Utility method to show diff-like output
    async showDiff(params) {
        try {
            const fileExists = await this.fileExists(params.path);
            if (!fileExists) {
                return `File does not exist: ${params.path}`;
            }
            const content = await fs.readFile(params.path, 'utf8');
            // const lines = content.split('\n');
            // const searchLines = params.search.split('\n');
            // const replaceLines = params.replace.split('\n');
            const diff = [];
            diff.push(`--- ${params.path} (original)`);
            diff.push(`+++ ${params.path} (modified)`);
            // Simple diff showing the change
            const searchIndex = content.indexOf(params.search);
            if (searchIndex !== -1) {
                diff.push(`@@ Search text found @@`);
                diff.push(`- ${params.search}`);
                diff.push(`+ ${params.replace}`);
            }
            else {
                diff.push(`No matches found for search text`);
            }
            return diff.join('\n');
        }
        catch (error) {
            return `Error generating diff: ${error}`;
        }
    }
}
//# sourceMappingURL=edit.js.map