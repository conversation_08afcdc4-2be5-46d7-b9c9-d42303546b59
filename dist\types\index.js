import { z } from 'zod';
// LLM Provider Types
export const LLMProviderSchema = z.enum(['deepseek', 'ollama']);
export const DeepseekModelSchema = z.enum(['deepseek-chat', 'deepseek-reasoner']);
// Configuration Types
export const ConfigSchema = z.object({
    provider: LLMProviderSchema,
    apiKey: z.string().optional(),
    model: z.string(),
    ollamaUrl: z.string().default('http://localhost:11434'),
    maxRetries: z.number().default(3),
    retryDelay: z.number().default(1000),
    timeout: z.number().default(30000),
});
// Tool Types
export const ToolTypeSchema = z.enum(['bash', 'grep', 'glob', 'write', 'edit', 'web']);
// Error Types
export class ArienError extends Error {
    code;
    retryable;
    constructor(message, code, retryable = false) {
        super(message);
        this.code = code;
        this.retryable = retryable;
        this.name = 'ArienError';
    }
}
//# sourceMappingURL=index.js.map