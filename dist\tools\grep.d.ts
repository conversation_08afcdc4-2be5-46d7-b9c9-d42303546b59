import { ToolResult } from '../types/index.js';
export interface GrepToolParams {
    pattern: string;
    path: string;
    recursive?: boolean;
    ignoreCase?: boolean;
    maxResults?: number;
}
export declare class GrepTool {
    execute(params: GrepToolParams): Promise<ToolResult>;
    private searchFiles;
    private searchInFile;
    private searchInDirectory;
    private isTextFile;
    private shouldSkipDirectory;
    private formatResults;
    private groupByFile;
    getDescription(): string;
    validateParams(params: any): params is GrepToolParams;
}
//# sourceMappingURL=grep.d.ts.map