import { RetryOptions, RetryState } from '../types/index.js';
export declare class RetryManager {
    private defaultOptions;
    private failurePatterns;
    private successPatterns;
    executeWithRetry<T>(operation: () => Promise<T>, options?: Partial<RetryOptions>, onRetry?: (state: RetryState) => void): Promise<T>;
    private isRetryableError;
    private trackFailurePattern;
    private extractErrorType;
    private calculateDelay;
    private sleep;
    executeWithRateLimit<T>(operation: () => Promise<T>, onRateLimit?: (retryAfter: number) => void): Promise<T>;
    private extractRetryAfter;
    executeWithNetworkRetry<T>(operation: () => Promise<T>, onNetworkError?: (attempt: number, error: Error) => void): Promise<T>;
    executeWithAlternatives<T>(primaryOperation: () => Promise<T>, alternativeOperations: Array<() => Promise<T>>, onAlternativeAttempt?: (alternativeIndex: number, error: Error) => void): Promise<T>;
    executeWithLearning<T>(operation: () => Promise<T>, operationContext: string, onLearning?: (pattern: string, successRate: number) => void): Promise<T>;
    private getAdaptiveMaxAttempts;
    private getAdaptiveBaseDelay;
    private trackSuccess;
    private trackFailure;
    private getSuccessRate;
    getRetryStatistics(): {
        failurePatterns: Map<string, number>;
        successPatterns: Map<string, number>;
        totalOperations: number;
        overallSuccessRate: number;
    };
    resetLearningData(): void;
}
//# sourceMappingURL=retry-logic.d.ts.map