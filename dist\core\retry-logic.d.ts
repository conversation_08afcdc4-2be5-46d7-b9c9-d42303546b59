import { RetryOptions, RetryState } from '../types/index.js';
export declare class RetryManager {
    private defaultOptions;
    executeWithRetry<T>(operation: () => Promise<T>, options?: Partial<RetryOptions>, onRetry?: (state: RetryState) => void): Promise<T>;
    private isRetryableError;
    private calculateDelay;
    private sleep;
    executeWithRateLimit<T>(operation: () => Promise<T>, onRateLimit?: (retryAfter: number) => void): Promise<T>;
    private extractRetryAfter;
    executeWithNetworkRetry<T>(operation: () => Promise<T>, onNetworkError?: (attempt: number, error: Error) => void): Promise<T>;
}
//# sourceMappingURL=retry-logic.d.ts.map