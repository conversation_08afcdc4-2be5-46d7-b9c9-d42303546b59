import chalk from 'chalk';
import inquirer from 'inquirer';
import { AnimationManager } from './animations.js';
export class UserInterface {
    animation;
    constructor() {
        this.animation = new AnimationManager();
    }
    showWelcome() {
        console.clear();
        console.log(chalk.cyan.bold(`
    ╔═══════════════════════════════════════╗
    ║           🤖 ARIEN AI CLI             ║
    ║     Advanced AI Terminal Assistant    ║
    ╚═══════════════════════════════════════╝
    `));
        console.log(chalk.gray('Powered by LLM Function Calling & Never Give Up Logic\n'));
    }
    async promptInitialSetup() {
        console.log(chalk.yellow('🔧 Initial Setup Required\n'));
        const { provider } = await inquirer.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select your LLM provider:',
                choices: [
                    { name: '🧠 Deepseek (deepseek-chat, deepseek-reasoner)', value: 'deepseek' },
                    { name: '🦙 Ollama (Local models)', value: 'ollama' }
                ]
            }
        ]);
        if (provider === 'deepseek') {
            return await this.setupDeepseek();
        }
        else {
            return await this.setupOllama();
        }
    }
    async setupDeepseek() {
        const answers = await inquirer.prompt([
            {
                type: 'password',
                name: 'api<PERSON><PERSON>',
                message: 'Enter your Deepseek API key:',
                mask: '*',
                validate: (input) => input.length > 0 || 'API key is required'
            },
            {
                type: 'list',
                name: 'model',
                message: 'Select Deepseek model:',
                choices: [
                    { name: 'deepseek-chat (Fast, general purpose)', value: 'deepseek-chat' },
                    { name: 'deepseek-reasoner (Advanced reasoning)', value: 'deepseek-reasoner' }
                ]
            }
        ]);
        return {
            provider: 'deepseek',
            apiKey: answers.apiKey,
            model: answers.model,
            ollamaUrl: 'http://localhost:11434',
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
    }
    async setupOllama() {
        const answers = await inquirer.prompt([
            {
                type: 'input',
                name: 'ollamaUrl',
                message: 'Ollama URL:',
                default: 'http://localhost:11434',
                validate: (input) => {
                    try {
                        new URL(input);
                        return true;
                    }
                    catch {
                        return 'Please enter a valid URL';
                    }
                }
            },
            {
                type: 'input',
                name: 'model',
                message: 'Model name (e.g., llama3.2, codellama, mistral):',
                validate: (input) => input.length > 0 || 'Model name is required'
            }
        ]);
        return {
            provider: 'ollama',
            model: answers.model,
            ollamaUrl: answers.ollamaUrl,
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
    }
    async promptUserInput() {
        const { input } = await inquirer.prompt([
            {
                type: 'input',
                name: 'input',
                message: chalk.cyan('You:'),
                validate: (input) => input.trim().length > 0 || 'Please enter a message'
            }
        ]);
        return input.trim();
    }
    async confirmAction(message) {
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: chalk.yellow(`⚠️  ${message}`),
                default: false
            }
        ]);
        return confirm;
    }
    showAIResponse(content) {
        console.log(`\n${chalk.green('🤖 Arien AI:')} ${content}\n`);
    }
    showToolExecution(toolType, parameters) {
        console.log(`${chalk.blue('🔧 Executing:')} ${chalk.cyan(toolType)} ${chalk.gray(JSON.stringify(parameters, null, 2))}`);
    }
    showToolResult(toolType, success, output, executionTime) {
        const status = success ? chalk.green('✓') : chalk.red('✗');
        const time = chalk.gray(`(${executionTime}ms)`);
        console.log(`${status} ${chalk.cyan(toolType)} ${time}`);
        if (output && output.trim()) {
            // Truncate very long outputs
            const truncatedOutput = output.length > 1000
                ? output.substring(0, 1000) + '\n... (output truncated)'
                : output;
            console.log(chalk.gray(truncatedOutput));
        }
        console.log();
    }
    showError(error) {
        this.animation.showError(error);
    }
    showWarning(warning) {
        this.animation.showWarning(warning);
    }
    showInfo(info) {
        this.animation.showInfo(info);
    }
    showSuccess(message) {
        this.animation.showSuccess(message);
    }
    startThinking(message = 'Processing your request...') {
        this.animation.start('thinking', message);
    }
    startProgress(message = 'Working...') {
        this.animation.start('ball', message);
    }
    stopAnimation() {
        this.animation.stop();
    }
    showProgress(current, total, message = '') {
        this.animation.showProgress(current, total, message);
    }
    showStep(step, total, message) {
        this.animation.showStep(step, total, message);
    }
    clearLine() {
        this.animation.clearLine();
    }
    newLine() {
        this.animation.newLine();
    }
    showConfigStatus(config) {
        console.log(chalk.blue('\n📋 Current Configuration:'));
        console.log(`   Provider: ${chalk.cyan(config.provider)}`);
        console.log(`   Model: ${chalk.cyan(config.model)}`);
        if (config.provider === 'deepseek') {
            console.log(`   API Key: ${chalk.gray(config.apiKey ? '***configured***' : 'not set')}`);
        }
        else {
            console.log(`   Ollama URL: ${chalk.cyan(config.ollamaUrl)}`);
        }
        console.log(`   Max Retries: ${chalk.cyan(config.maxRetries)}`);
        console.log(`   Timeout: ${chalk.cyan(config.timeout)}ms\n`);
    }
}
//# sourceMappingURL=interface.js.map