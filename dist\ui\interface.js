import chalk from 'chalk';
import inquirer from 'inquirer';
import { AnimationManager } from './animations.js';
export class UserInterface {
    animation;
    constructor() {
        this.animation = new AnimationManager();
    }
    showWelcome() {
        console.clear();
        console.log(chalk.cyan.bold(`
    ╔═══════════════════════════════════════╗
    ║           🤖 ARIEN AI CLI             ║
    ║     Advanced AI Terminal Assistant    ║
    ╚═══════════════════════════════════════╝
    `));
        console.log(chalk.gray('Powered by LLM Function Calling & Never Give Up Logic\n'));
    }
    async promptInitialSetup() {
        console.log(chalk.yellow('🔧 Initial Setup Required\n'));
        const { provider } = await inquirer.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select your LLM provider:',
                choices: [
                    { name: '🧠 Deepseek (deepseek-chat, deepseek-reasoner)', value: 'deepseek' },
                    { name: '🦙 Ollama (Local models)', value: 'ollama' }
                ]
            }
        ]);
        if (provider === 'deepseek') {
            return await this.setupDeepseek();
        }
        else {
            return await this.setupOllama();
        }
    }
    async setupDeepseek() {
        const answers = await inquirer.prompt([
            {
                type: 'password',
                name: 'api<PERSON><PERSON>',
                message: 'Enter your Deepseek API key:',
                mask: '*',
                validate: (input) => input.length > 0 || 'API key is required'
            },
            {
                type: 'list',
                name: 'model',
                message: 'Select Deepseek model:',
                choices: [
                    { name: 'deepseek-chat (Fast, general purpose)', value: 'deepseek-chat' },
                    { name: 'deepseek-reasoner (Advanced reasoning)', value: 'deepseek-reasoner' }
                ]
            }
        ]);
        return {
            provider: 'deepseek',
            apiKey: answers.apiKey,
            model: answers.model,
            ollamaUrl: 'http://localhost:11434',
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
    }
    async setupOllama() {
        const answers = await inquirer.prompt([
            {
                type: 'input',
                name: 'ollamaUrl',
                message: 'Ollama URL:',
                default: 'http://localhost:11434',
                validate: (input) => {
                    try {
                        new URL(input);
                        return true;
                    }
                    catch {
                        return 'Please enter a valid URL';
                    }
                }
            },
            {
                type: 'input',
                name: 'model',
                message: 'Model name (e.g., llama3.2, codellama, mistral):',
                validate: (input) => input.length > 0 || 'Model name is required'
            }
        ]);
        return {
            provider: 'ollama',
            model: answers.model,
            ollamaUrl: answers.ollamaUrl,
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
    }
    async promptUserInput() {
        const { input } = await inquirer.prompt([
            {
                type: 'input',
                name: 'input',
                message: chalk.cyan('You:'),
                validate: (input) => input.trim().length > 0 || 'Please enter a message'
            }
        ]);
        return input.trim();
    }
    async confirmAction(message) {
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: chalk.yellow(`⚠️  ${message}`),
                default: false
            }
        ]);
        return confirm;
    }
    showAIResponse(content) {
        console.log(`\n${chalk.green('🤖 Arien AI:')} ${content}\n`);
    }
    showToolExecution(toolType, parameters) {
        // Show tool execution without revealing sensitive parameters
        const sanitizedParams = this.sanitizeParameters(parameters);
        const paramStr = Object.keys(sanitizedParams).length > 0
            ? chalk.gray(`(${Object.keys(sanitizedParams).join(', ')})`)
            : '';
        console.log(`${chalk.blue('🔧 Executing:')} ${chalk.cyan(toolType)} ${paramStr}`);
    }
    showToolResult(toolType, success, output, executionTime) {
        const status = success ? chalk.green('✓') : chalk.red('✗');
        const time = chalk.gray(`(${executionTime}ms)`);
        console.log(`${status} ${chalk.cyan(toolType)} completed ${time}`);
        // Only show output summary, not full output (as per requirements)
        if (output && output.trim()) {
            const summary = this.generateOutputSummary(output, success);
            if (summary) {
                console.log(chalk.gray(`   ${summary}`));
            }
        }
        console.log();
    }
    sanitizeParameters(params) {
        const sanitized = { ...params };
        // Remove or mask sensitive information
        const sensitiveKeys = ['password', 'apiKey', 'token', 'secret', 'key'];
        for (const key of sensitiveKeys) {
            if (sanitized[key]) {
                sanitized[key] = '***';
            }
        }
        // Truncate long values
        for (const [key, value] of Object.entries(sanitized)) {
            if (typeof value === 'string' && value.length > 50) {
                sanitized[key] = value.substring(0, 47) + '...';
            }
        }
        return sanitized;
    }
    generateOutputSummary(output, success) {
        if (!success) {
            // For errors, show first line of error message
            const firstLine = output.split('\n')[0] || 'Unknown error';
            return `Error: ${firstLine.substring(0, 100)}${firstLine.length > 100 ? '...' : ''}`;
        }
        // For successful operations, provide meaningful summaries
        const lines = output.split('\n').filter(line => line.trim());
        if (lines.length === 0) {
            return 'Operation completed successfully';
        }
        if (lines.length === 1) {
            const line = lines[0] || '';
            return line.substring(0, 100) + (line.length > 100 ? '...' : '');
        }
        // Multi-line output summary
        if (output.includes('STDOUT:') || output.includes('STDERR:')) {
            return `Command output: ${lines.length} lines`;
        }
        if (output.includes('files found') || output.includes('matches')) {
            return `Search completed: ${lines.length} results`;
        }
        return `Operation completed: ${lines.length} lines of output`;
    }
    showError(error) {
        this.animation.showError(error);
    }
    showWarning(warning) {
        this.animation.showWarning(warning);
    }
    showInfo(info) {
        this.animation.showInfo(info);
    }
    showSuccess(message) {
        this.animation.showSuccess(message);
    }
    startThinking(message = 'Processing your request...') {
        this.animation.start('thinking', message);
    }
    startProgress(message = 'Working...') {
        this.animation.start('ball', message);
    }
    stopAnimation() {
        this.animation.stop();
    }
    showProgress(current, total, message = '') {
        this.animation.showProgress(current, total, message);
    }
    showStep(step, total, message) {
        this.animation.showStep(step, total, message);
    }
    clearLine() {
        this.animation.clearLine();
    }
    newLine() {
        this.animation.newLine();
    }
    showConfigStatus(config) {
        console.log(chalk.blue('\n📋 Current Configuration:'));
        console.log(`   Provider: ${chalk.cyan(config.provider)}`);
        console.log(`   Model: ${chalk.cyan(config.model)}`);
        if (config.provider === 'deepseek') {
            console.log(`   API Key: ${chalk.gray(config.apiKey ? '***configured***' : 'not set')}`);
        }
        else {
            console.log(`   Ollama URL: ${chalk.cyan(config.ollamaUrl)}`);
        }
        console.log(`   Max Retries: ${chalk.cyan(config.maxRetries)}`);
        console.log(`   Timeout: ${chalk.cyan(config.timeout)}ms\n`);
    }
    // Enhanced real-time interface methods
    showThinkingProcess(message) {
        console.log(`${chalk.yellow('🧠 Thinking:')} ${chalk.gray(message)}`);
    }
    showPlanningPhase(steps) {
        console.log(`${chalk.blue('📋 Planning:')} ${chalk.gray('Breaking down your request...')}`);
        steps.forEach((step, index) => {
            console.log(`   ${chalk.cyan(`${index + 1}.`)} ${step}`);
        });
        console.log();
    }
    showExecutionPhase(currentStep, totalSteps, stepDescription) {
        const progress = `[${currentStep}/${totalSteps}]`;
        console.log(`${chalk.green('⚡ Executing:')} ${chalk.cyan(progress)} ${stepDescription}`);
    }
    showRetryAttempt(attempt, maxAttempts, reason) {
        console.log(`${chalk.yellow('🔄 Retry:')} ${chalk.gray(`Attempt ${attempt}/${maxAttempts} - ${reason}`)}`);
    }
    showAlternativeStrategy(strategy) {
        console.log(`${chalk.magenta('🔀 Alternative:')} ${chalk.gray(strategy)}`);
    }
    showProgressUpdate(message, percentage) {
        const percentStr = percentage !== undefined ? chalk.cyan(`${percentage}%`) : '';
        console.log(`${chalk.blue('📊 Progress:')} ${message} ${percentStr}`);
    }
    showStreamingResponse(chunk) {
        // For streaming AI responses
        process.stdout.write(chunk);
    }
    showToolOutputSummary(toolType, summary) {
        console.log(`${chalk.gray('   →')} ${chalk.cyan(toolType)}: ${summary}`);
    }
    showDecisionRationale(decision, reasoning) {
        console.log(`${chalk.blue('🤔 Decision:')} ${decision}`);
        console.log(`${chalk.gray('   Reasoning:')} ${reasoning}`);
    }
    showResourceUsage(stats) {
        const parts = [];
        if (stats.memory)
            parts.push(`Memory: ${stats.memory}MB`);
        if (stats.cpu)
            parts.push(`CPU: ${stats.cpu}%`);
        if (stats.time)
            parts.push(`Time: ${stats.time}ms`);
        if (parts.length > 0) {
            console.log(`${chalk.gray('📈 Resources:')} ${parts.join(' | ')}`);
        }
    }
    showCompletionSummary(totalSteps, successfulSteps, totalTime) {
        const successRate = Math.round((successfulSteps / totalSteps) * 100);
        const timeStr = totalTime > 1000 ? `${(totalTime / 1000).toFixed(1)}s` : `${totalTime}ms`;
        console.log(`\n${chalk.green('✅ Completed:')} ${successfulSteps}/${totalSteps} steps (${successRate}%) in ${timeStr}`);
    }
    showInteractivePrompt(message, options) {
        console.log(`\n${chalk.yellow('❓ Input needed:')} ${message}`);
        if (options && options.length > 0) {
            options.forEach((option, index) => {
                console.log(`   ${chalk.cyan(`${index + 1}.`)} ${option}`);
            });
        }
    }
    clearCurrentLine() {
        process.stdout.write('\r\x1b[K');
    }
    moveCursorUp(lines = 1) {
        process.stdout.write(`\x1b[${lines}A`);
    }
    moveCursorDown(lines = 1) {
        process.stdout.write(`\x1b[${lines}B`);
    }
    saveCurrentPosition() {
        process.stdout.write('\x1b[s');
    }
    restorePosition() {
        process.stdout.write('\x1b[u');
    }
}
//# sourceMappingURL=interface.js.map