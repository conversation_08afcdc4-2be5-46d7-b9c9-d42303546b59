import { ToolResult } from '../types/index.js';
export interface WriteToolParams {
    path: string;
    content: string;
    encoding?: BufferEncoding;
    createDirectories?: boolean;
}
export declare class WriteTool {
    execute(params: WriteToolParams): Promise<ToolResult>;
    private writeFile;
    private fileExists;
    getDescription(): string;
    validateParams(params: any): params is WriteToolParams;
    validatePath(path: string): {
        valid: boolean;
        error?: string;
    };
    getFileInfo(path: string): Promise<{
        exists: boolean;
        size?: number;
        modified?: Date;
    }>;
    backupFile(path: string): Promise<string | null>;
}
//# sourceMappingURL=write.d.ts.map