import { ArienError } from '../types/index.js';
export class RetryManager {
    defaultOptions = {
        maxAttempts: 5, // Increased for better persistence
        baseDelay: 1000,
        maxDelay: 60000, // Increased max delay for better handling of severe rate limits
        exponentialBase: 2,
        jitter: true
    };
    failurePatterns = new Map();
    successPatterns = new Map();
    async executeWithRetry(operation, options = {}, onRetry) {
        const opts = { ...this.defaultOptions, ...options };
        const state = {
            attempt: 0,
            totalElapsed: 0
        };
        const startTime = Date.now();
        while (state.attempt < opts.maxAttempts) {
            state.attempt++;
            try {
                const result = await operation();
                return result;
            }
            catch (error) {
                state.lastError = error;
                state.totalElapsed = Date.now() - startTime;
                // Check if error is retryable
                if (!this.isRetryableError(error)) {
                    throw error;
                }
                // If this was the last attempt, throw the error
                if (state.attempt >= opts.maxAttempts) {
                    throw new ArienError(`Operation failed after ${opts.maxAttempts} attempts: ${state.lastError.message}`, 'MAX_RETRIES_EXCEEDED', false);
                }
                // Calculate delay for next attempt
                const delay = this.calculateDelay(state.attempt, opts);
                // Notify about retry
                if (onRetry) {
                    onRetry(state);
                }
                // Wait before retrying
                await this.sleep(delay);
            }
        }
        // This should never be reached, but TypeScript requires it
        throw state.lastError || new Error('Unknown error occurred');
    }
    isRetryableError(error) {
        const errorMessage = error.message.toLowerCase();
        // Track failure patterns for learning
        this.trackFailurePattern(errorMessage);
        // Network connectivity errors (always retryable)
        if (errorMessage.includes('econnreset') ||
            errorMessage.includes('enotfound') ||
            errorMessage.includes('etimedout') ||
            errorMessage.includes('econnrefused') ||
            errorMessage.includes('network error') ||
            errorMessage.includes('connection failed') ||
            errorMessage.includes('socket hang up') ||
            errorMessage.includes('connect timeout')) {
            return true;
        }
        // Rate limiting and throttling (retryable with backoff)
        if (errorMessage.includes('rate limit') ||
            errorMessage.includes('429') ||
            errorMessage.includes('too many requests') ||
            errorMessage.includes('quota exceeded') ||
            errorMessage.includes('throttled') ||
            errorMessage.includes('rate exceeded')) {
            return true;
        }
        // Server errors (5xx - usually temporary)
        if (errorMessage.includes('500') ||
            errorMessage.includes('502') ||
            errorMessage.includes('503') ||
            errorMessage.includes('504') ||
            errorMessage.includes('internal server error') ||
            errorMessage.includes('bad gateway') ||
            errorMessage.includes('service unavailable') ||
            errorMessage.includes('gateway timeout')) {
            return true;
        }
        // Temporary service issues
        if (errorMessage.includes('temporarily unavailable') ||
            errorMessage.includes('service temporarily overloaded') ||
            errorMessage.includes('maintenance mode') ||
            errorMessage.includes('try again later')) {
            return true;
        }
        // Resource contention (often temporary)
        if (errorMessage.includes('resource busy') ||
            errorMessage.includes('lock timeout') ||
            errorMessage.includes('deadlock') ||
            errorMessage.includes('resource temporarily unavailable')) {
            return true;
        }
        // Client errors that are NOT retryable (4xx except 429)
        if (errorMessage.includes('400') ||
            errorMessage.includes('401') ||
            errorMessage.includes('403') ||
            errorMessage.includes('404') ||
            errorMessage.includes('422') ||
            errorMessage.includes('bad request') ||
            errorMessage.includes('unauthorized') ||
            errorMessage.includes('forbidden') ||
            errorMessage.includes('not found') ||
            errorMessage.includes('unprocessable entity') ||
            errorMessage.includes('invalid parameter') ||
            errorMessage.includes('malformed request')) {
            return false;
        }
        // ArienError with explicit retryable flag
        if (error instanceof ArienError) {
            return error.retryable;
        }
        // Default to non-retryable for unknown errors to prevent infinite loops
        return false;
    }
    trackFailurePattern(errorMessage) {
        // Extract error type for pattern tracking
        const errorType = this.extractErrorType(errorMessage);
        const currentCount = this.failurePatterns.get(errorType) || 0;
        this.failurePatterns.set(errorType, currentCount + 1);
    }
    extractErrorType(errorMessage) {
        // Extract meaningful error categories for pattern analysis
        if (errorMessage.includes('network') || errorMessage.includes('connection'))
            return 'network';
        if (errorMessage.includes('rate') || errorMessage.includes('429'))
            return 'rate_limit';
        if (errorMessage.includes('timeout'))
            return 'timeout';
        if (errorMessage.includes('5'))
            return 'server_error';
        if (errorMessage.includes('4'))
            return 'client_error';
        return 'unknown';
    }
    calculateDelay(attempt, options) {
        // Exponential backoff: baseDelay * (exponentialBase ^ (attempt - 1))
        let delay = options.baseDelay * Math.pow(options.exponentialBase, attempt - 1);
        // Cap at maxDelay
        delay = Math.min(delay, options.maxDelay);
        // Add jitter to prevent thundering herd
        if (options.jitter) {
            delay = delay * (0.5 + Math.random() * 0.5);
        }
        return Math.floor(delay);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Specialized retry for rate limiting
    async executeWithRateLimit(operation, onRateLimit) {
        return this.executeWithRetry(operation, {
            maxAttempts: 5,
            baseDelay: 2000,
            maxDelay: 60000,
            exponentialBase: 2,
            jitter: true
        }, (state) => {
            if (state.lastError?.message.includes('rate limit') && onRateLimit) {
                // Extract retry-after header if available
                const retryAfter = this.extractRetryAfter(state.lastError.message);
                onRateLimit(retryAfter);
            }
        });
    }
    extractRetryAfter(errorMessage) {
        // Try to extract retry-after value from error message
        const match = errorMessage.match(/retry.after[:\s]+(\d+)/i);
        return match && match[1] ? parseInt(match[1]) * 1000 : 5000; // Default to 5 seconds
    }
    // Specialized retry for network operations with adaptive strategies
    async executeWithNetworkRetry(operation, onNetworkError) {
        return this.executeWithRetry(operation, {
            maxAttempts: 6, // More attempts for network issues
            baseDelay: 1000,
            maxDelay: 15000,
            exponentialBase: 2,
            jitter: true
        }, (state) => {
            if (onNetworkError && state.lastError) {
                onNetworkError(state.attempt, state.lastError);
            }
        });
    }
    // Advanced retry with alternative strategies
    async executeWithAlternatives(primaryOperation, alternativeOperations, onAlternativeAttempt) {
        // Try primary operation first
        try {
            return await this.executeWithRetry(primaryOperation, {
                maxAttempts: 3,
                baseDelay: 1000,
                maxDelay: 10000,
                exponentialBase: 2,
                jitter: true
            });
        }
        catch (primaryError) {
            // If primary fails, try alternatives
            for (let i = 0; i < alternativeOperations.length; i++) {
                try {
                    if (onAlternativeAttempt) {
                        onAlternativeAttempt(i, primaryError);
                    }
                    return await this.executeWithRetry(alternativeOperations[i], {
                        maxAttempts: 2, // Fewer attempts for alternatives
                        baseDelay: 500,
                        maxDelay: 5000,
                        exponentialBase: 1.5,
                        jitter: true
                    });
                }
                catch (alternativeError) {
                    // Continue to next alternative
                    continue;
                }
            }
            // If all alternatives fail, throw the original error
            throw new ArienError(`Primary operation and all ${alternativeOperations.length} alternatives failed. Last error: ${primaryError}`, 'ALL_ALTERNATIVES_FAILED', false);
        }
    }
    // Intelligent retry with context learning
    async executeWithLearning(operation, operationContext, onLearning) {
        try {
            const result = await this.executeWithRetry(operation, {
                maxAttempts: this.getAdaptiveMaxAttempts(operationContext),
                baseDelay: this.getAdaptiveBaseDelay(operationContext),
                maxDelay: 30000,
                exponentialBase: 2,
                jitter: true
            });
            // Track success
            this.trackSuccess(operationContext);
            if (onLearning) {
                const successRate = this.getSuccessRate(operationContext);
                onLearning(operationContext, successRate);
            }
            return result;
        }
        catch (error) {
            // Track failure for learning
            this.trackFailure(operationContext);
            throw error;
        }
    }
    getAdaptiveMaxAttempts(context) {
        const failureCount = this.failurePatterns.get(context) || 0;
        const successCount = this.successPatterns.get(context) || 0;
        // More attempts for contexts with higher historical success rates
        if (successCount > failureCount * 2)
            return 7; // High success rate
        if (successCount > failureCount)
            return 5; // Moderate success rate
        return 3; // Low success rate or new context
    }
    getAdaptiveBaseDelay(context) {
        const failureCount = this.failurePatterns.get(context) || 0;
        // Longer delays for contexts with frequent failures
        if (failureCount > 10)
            return 2000; // High failure rate
        if (failureCount > 5)
            return 1500; // Moderate failure rate
        return 1000; // Low failure rate or new context
    }
    trackSuccess(context) {
        const currentCount = this.successPatterns.get(context) || 0;
        this.successPatterns.set(context, currentCount + 1);
    }
    trackFailure(context) {
        const currentCount = this.failurePatterns.get(context) || 0;
        this.failurePatterns.set(context, currentCount + 1);
    }
    getSuccessRate(context) {
        const successCount = this.successPatterns.get(context) || 0;
        const failureCount = this.failurePatterns.get(context) || 0;
        const total = successCount + failureCount;
        return total > 0 ? successCount / total : 0;
    }
    // Get retry statistics for monitoring and debugging
    getRetryStatistics() {
        const totalFailures = Array.from(this.failurePatterns.values()).reduce((sum, count) => sum + count, 0);
        const totalSuccesses = Array.from(this.successPatterns.values()).reduce((sum, count) => sum + count, 0);
        const totalOperations = totalFailures + totalSuccesses;
        const overallSuccessRate = totalOperations > 0 ? totalSuccesses / totalOperations : 0;
        return {
            failurePatterns: new Map(this.failurePatterns),
            successPatterns: new Map(this.successPatterns),
            totalOperations,
            overallSuccessRate
        };
    }
    // Reset learning data (useful for testing or fresh starts)
    resetLearningData() {
        this.failurePatterns.clear();
        this.successPatterns.clear();
    }
}
//# sourceMappingURL=retry-logic.js.map