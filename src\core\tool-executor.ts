import { Tool<PERSON><PERSON>, Tool<PERSON><PERSON><PERSON>, ToolType, ExecutionContext, ArienError } from '../types/index.js';
import { BashTool } from '../tools/bash.js';
import { GrepTool } from '../tools/grep.js';
import { GlobTool } from '../tools/glob.js';
import { WriteTool } from '../tools/write.js';
import { EditTool } from '../tools/edit.js';
import { WebTool } from '../tools/web.js';
import { RetryManager } from './retry-logic.js';

export class ToolExecutor {
  private bashTool: BashTool;
  private grepTool: GrepTool;
  private globTool: GlobTool;
  private writeTool: WriteTool;
  private editTool: EditTool;
  private webTool: WebTool;
  private retryManager: RetryManager;

  constructor() {
    this.bashTool = new BashTool();
    this.grepTool = new GrepTool();
    this.globTool = new GlobTool();
    this.writeTool = new WriteTool();
    this.editTool = new EditTool();
    this.webTool = new WebTool();
    this.retryManager = new RetryManager();
  }

  async executeTool(toolCall: ToolCall, context: ExecutionContext): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate tool parameters
      if (!this.validateToolCall(toolCall)) {
        throw new ArienError(
          `Invalid parameters for tool: ${toolCall.type}`,
          'INVALID_TOOL_PARAMS'
        );
      }

      // Execute the tool with intelligent retry logic based on tool type
      return await this.retryManager.executeWithLearning(async () => {
        switch (toolCall.type) {
          case 'bash':
            return await this.bashTool.execute(toolCall.parameters as any, context);

          case 'grep':
            return await this.grepTool.execute(toolCall.parameters as any);

          case 'glob':
            return await this.globTool.execute(toolCall.parameters as any);

          case 'write':
            return await this.writeTool.execute(toolCall.parameters as any);

          case 'edit':
            return await this.editTool.execute(toolCall.parameters as any);

          case 'web':
            return await this.webTool.execute(toolCall.parameters as any);

          default:
            throw new ArienError(
              `Unknown tool type: ${toolCall.type}`,
              'UNKNOWN_TOOL_TYPE'
            );
        }
      }, `tool_${toolCall.type}`, (_pattern, successRate) => {
        // Learning callback - could be used for optimization
        console.debug(`Tool ${toolCall.type} success rate: ${(successRate * 100).toFixed(1)}%`);
      });

    } catch (error) {
      return {
        id: toolCall.id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : String(error),
        executionTime: Date.now() - startTime
      };
    }
  }



  async executeToolsParallel(toolCalls: ToolCall[], context: ExecutionContext): Promise<ToolResult[]> {
    // Execute tools in parallel for independent operations with intelligent batching
    const batchSize = this.calculateOptimalBatchSize(toolCalls);
    const results: ToolResult[] = [];

    // Process in batches to avoid overwhelming the system
    for (let i = 0; i < toolCalls.length; i += batchSize) {
      const batch = toolCalls.slice(i, i + batchSize);
      const batchPromises = batch.map(toolCall => this.executeTool(toolCall, context));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches to prevent resource exhaustion
      if (i + batchSize < toolCalls.length) {
        await this.sleep(100);
      }
    }

    return results;
  }

  private calculateOptimalBatchSize(toolCalls: ToolCall[]): number {
    // Determine optimal batch size based on tool types and system resources
    const heavyTools = ['bash', 'web'].filter(type =>
      toolCalls.some(call => call.type === type)
    ).length;

    const lightTools = ['grep', 'glob', 'write', 'edit'].filter(type =>
      toolCalls.some(call => call.type === type)
    ).length;

    // Adjust batch size based on tool complexity
    if (heavyTools > lightTools) {
      return Math.min(3, toolCalls.length); // Smaller batches for heavy tools
    } else {
      return Math.min(6, toolCalls.length); // Larger batches for light tools
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async executeToolsSequential(toolCalls: ToolCall[], context: ExecutionContext): Promise<ToolResult[]> {
    // Execute tools sequentially for dependent operations with enhanced failure handling
    const results: ToolResult[] = [];

    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      if (!toolCall) continue; // Skip undefined entries

      const result = await this.executeTool(toolCall, context);
      results.push(result);

      // Enhanced failure handling with context awareness
      if (!result.success) {
        const shouldContinue = await this.handleSequentialFailure(
          toolCall,
          result,
          i,
          toolCalls.length,
          context
        );

        if (!shouldContinue) {
          break;
        }
      }

      // Update context based on successful tool execution
      if (result.success) {
        this.updateContextFromResult(toolCall, result, context);
      }
    }

    return results;
  }

  private async handleSequentialFailure(
    failedTool: ToolCall,
    result: ToolResult,
    _currentIndex: number,
    _totalTools: number,
    context: ExecutionContext
  ): Promise<boolean> {
    // Determine if we should continue the sequence after a failure

    // Critical tools that should stop the sequence
    if (this.isCriticalTool(failedTool.type)) {
      return false;
    }

    // If it's a dependency-based failure, try alternative approaches
    if (this.isDependencyFailure(result.error)) {
      // Try to recover or find alternatives
      const recovered = await this.attemptRecovery(failedTool, context);
      return recovered;
    }

    // For non-critical tools, continue with remaining tools
    return true;
  }

  private updateContextFromResult(toolCall: ToolCall, _result: ToolResult, context: ExecutionContext): void {
    // Update execution context based on successful tool results
    switch (toolCall.type) {
      case 'bash':
        // Update working directory if cd command was used
        const command = toolCall.parameters['command'] as string;
        if (command?.includes('cd ')) {
          // Extract new directory from command (simplified)
          const cdMatch = command.match(/cd\s+([^\s&|;]+)/);
          if (cdMatch && cdMatch[1]) {
            // This is a simplified approach - in reality, you'd need more sophisticated path resolution
            context.workingDirectory = cdMatch[1];
          }
        }
        break;

      case 'write':
      case 'edit':
        // Files were modified - could update file tracking if needed
        break;

      default:
        // No context updates needed for other tools
        break;
    }
  }

  private async attemptRecovery(failedTool: ToolCall, context: ExecutionContext): Promise<boolean> {
    // Attempt to recover from tool failures with alternative approaches

    switch (failedTool.type) {
      case 'bash':
        // Try alternative commands or simplified versions
        return await this.tryAlternativeBashCommand(failedTool, context);

      case 'web':
        // Try alternative search terms or different endpoints
        return await this.tryAlternativeWebSearch(failedTool);

      default:
        return false;
    }
  }

  private async tryAlternativeBashCommand(_toolCall: ToolCall, _context: ExecutionContext): Promise<boolean> {
    // Implement alternative bash command strategies
    // This is a placeholder for more sophisticated recovery logic
    return false;
  }

  private async tryAlternativeWebSearch(_toolCall: ToolCall): Promise<boolean> {
    // Implement alternative web search strategies
    // This is a placeholder for more sophisticated recovery logic
    return false;
  }

  private isDependencyFailure(error?: string): boolean {
    if (!error) return false;

    const dependencyIndicators = [
      'command not found',
      'no such file or directory',
      'permission denied',
      'network unreachable',
      'connection refused'
    ];

    return dependencyIndicators.some(indicator =>
      error.toLowerCase().includes(indicator)
    );
  }

  private validateToolCall(toolCall: ToolCall): boolean {
    switch (toolCall.type) {
      case 'bash':
        return this.bashTool.validateParams(toolCall.parameters);
      
      case 'grep':
        return this.grepTool.validateParams(toolCall.parameters);
      
      case 'glob':
        return this.globTool.validateParams(toolCall.parameters);
      
      case 'write':
        return this.writeTool.validateParams(toolCall.parameters);
      
      case 'edit':
        return this.editTool.validateParams(toolCall.parameters);
      
      case 'web':
        return this.webTool.validateParams(toolCall.parameters);
      
      default:
        return false;
    }
  }

  private isCriticalTool(toolType: ToolType): boolean {
    // Define which tools are critical and should stop execution on failure
    const criticalTools: ToolType[] = ['bash', 'write', 'edit'];
    return criticalTools.includes(toolType);
  }

  getToolDescription(toolType: ToolType): string {
    switch (toolType) {
      case 'bash':
        return this.bashTool.getDescription();
      
      case 'grep':
        return this.grepTool.getDescription();
      
      case 'glob':
        return this.globTool.getDescription();
      
      case 'write':
        return this.writeTool.getDescription();
      
      case 'edit':
        return this.editTool.getDescription();
      
      case 'web':
        return this.webTool.getDescription();
      
      default:
        return 'Unknown tool';
    }
  }

  getAllToolDescriptions(): Record<ToolType, string> {
    return {
      bash: this.bashTool.getDescription(),
      grep: this.grepTool.getDescription(),
      glob: this.globTool.getDescription(),
      write: this.writeTool.getDescription(),
      edit: this.editTool.getDescription(),
      web: this.webTool.getDescription()
    };
  }

  // Utility method to determine execution strategy
  determineExecutionStrategy(toolCalls: ToolCall[]): 'parallel' | 'sequential' {
    // If any tool depends on file system state changes, use sequential
    const fileModifyingTools: ToolType[] = ['write', 'edit', 'bash'];
    const hasFileModification = toolCalls.some(call => fileModifyingTools.includes(call.type));
    
    // If tools are reading from the same sources, use sequential to avoid conflicts
    const readingTools: ToolType[] = ['grep', 'glob'];
    const hasMultipleReads = toolCalls.filter(call => readingTools.includes(call.type)).length > 1;
    
    // If there are bash commands, they might depend on each other
    const hasBashCommands = toolCalls.some(call => call.type === 'bash');
    
    if (hasFileModification || hasMultipleReads || hasBashCommands) {
      return 'sequential';
    }
    
    return 'parallel';
  }

  // Utility method to estimate execution time
  estimateExecutionTime(toolCalls: ToolCall[]): number {
    const timeEstimates: Record<ToolType, number> = {
      bash: 5000,    // 5 seconds for bash commands
      grep: 2000,    // 2 seconds for file search
      glob: 1000,    // 1 second for file pattern matching
      write: 500,    // 0.5 seconds for file writing
      edit: 1000,    // 1 second for file editing
      web: 10000     // 10 seconds for web requests
    };

    const strategy = this.determineExecutionStrategy(toolCalls);
    
    if (strategy === 'parallel') {
      // Return the maximum time (longest running tool)
      return Math.max(...toolCalls.map(call => timeEstimates[call.type] || 1000));
    } else {
      // Return the sum of all times
      return toolCalls.reduce((total, call) => total + (timeEstimates[call.type] || 1000), 0);
    }
  }
}
