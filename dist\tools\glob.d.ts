import { ToolResult } from '../types/index.js';
export interface GlobToolParams {
    pattern: string;
    cwd?: string;
    absolute?: boolean;
    maxResults?: number;
}
export declare class GlobTool {
    execute(params: GlobToolParams): Promise<ToolResult>;
    private findFiles;
    private formatResults;
    private formatFileSize;
    getDescription(): string;
    validateParams(params: any): params is GlobToolParams;
}
//# sourceMappingURL=glob.d.ts.map