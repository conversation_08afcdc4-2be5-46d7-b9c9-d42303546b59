{"version": 3, "file": "retry-logic.d.ts", "sourceRoot": "", "sources": ["../../src/core/retry-logic.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAc,MAAM,mBAAmB,CAAC;AAEzE,qBAAa,YAAY;IACvB,OAAO,CAAC,cAAc,CAMpB;IAEF,OAAO,CAAC,eAAe,CAAkC;IACzD,OAAO,CAAC,eAAe,CAAkC;IAEnD,gBAAgB,CAAC,CAAC,EACtB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM,EACnC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,IAAI,GACpC,OAAO,CAAC,CAAC,CAAC;IAkDb,OAAO,CAAC,gBAAgB;IAiFxB,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,gBAAgB;IAUxB,OAAO,CAAC,cAAc;IAetB,OAAO,CAAC,KAAK;IAKP,oBAAoB,CAAC,CAAC,EAC1B,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,KAAK,IAAI,GACzC,OAAO,CAAC,CAAC,CAAC;IAoBb,OAAO,CAAC,iBAAiB;IAOnB,uBAAuB,CAAC,CAAC,EAC7B,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,GACvD,OAAO,CAAC,CAAC,CAAC;IAmBP,uBAAuB,CAAC,CAAC,EAC7B,gBAAgB,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAClC,qBAAqB,EAAE,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,EAC9C,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,GACtE,OAAO,CAAC,CAAC,CAAC;IAyCP,mBAAmB,CAAC,CAAC,EACzB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,gBAAgB,EAAE,MAAM,EACxB,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,KAAK,IAAI,GAC1D,OAAO,CAAC,CAAC,CAAC;IA2Bb,OAAO,CAAC,sBAAsB;IAU9B,OAAO,CAAC,oBAAoB;IAS5B,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,cAAc;IAStB,kBAAkB,IAAI;QACpB,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrC,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrC,eAAe,EAAE,MAAM,CAAC;QACxB,kBAAkB,EAAE,MAAM,CAAC;KAC5B;IAeD,iBAAiB,IAAI,IAAI;CAI1B"}