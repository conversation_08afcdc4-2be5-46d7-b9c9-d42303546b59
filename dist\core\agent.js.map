{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../src/core/agent.ts"], "names": [], "mappings": "AAAA,OAAO,EAA2D,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACxG,OAAO,EAAE,iBAAiB,EAAe,MAAM,oBAAoB,CAAC;AACpE,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,MAAM,OAAO,UAAU;IACb,WAAW,CAAc;IACzB,YAAY,CAAe;IAC3B,YAAY,CAAe;IAC3B,EAAE,CAAgB;IAClB,QAAQ,GAAc,EAAE,CAAC;IACzB,OAAO,CAAmB;IAElC,YAAY,MAAc,EAAE,EAAiB;QAC3C,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QAEb,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,WAAW,EAAE,OAAO,CAAC,GAA6B;YAClD,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC;QAEF,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,aAAa;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa;QAClC,IAAI,CAAC;YACH,mBAAmB;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,2BAA2B;YAC3B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;YAEpD,4CAA4C;YAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBAClD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnC,CAAC,EAAE;gBACD,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;gBACf,eAAe,EAAE,CAAC;aACnB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;YAExB,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;gBAChC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,aAAa,GAAG,EAAE,CAAC,CAAC,yBAAyB;QACjD,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,SAAS,GAAG,aAAa,EAAE,CAAC;YACjC,SAAS,EAAE,CAAC;YAEZ,IAAI,CAAC;gBACH,mBAAmB;gBACnB,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,0BAA0B,SAAS,GAAG,CAAC,CAAC;gBAC9D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxE,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;gBAExB,sCAAsC;gBACtC,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;oBAChD,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAC3C,CAAC;gBAED,wBAAwB;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,SAAS;oBAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3D,MAAM;gBACR,CAAC;gBAED,gBAAgB;gBAChB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAEhE,mFAAmF;gBACnF,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBAC7C,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;oBAClC,IAAI,MAAM,EAAE,CAAC;wBACX,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;4BACjB,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,MAAM,CAAC,OAAO;gCACrB,CAAC,CAAC,QAAQ,QAAQ,CAAC,IAAI,gBAAgB,MAAM,CAAC,MAAM,EAAE;gCACtD,CAAC,CAAC,QAAQ,QAAQ,CAAC,IAAI,YAAY,MAAM,CAAC,KAAK,EAAE;4BACnD,WAAW,EAAE,CAAC,MAAM,CAAC;4BACrB,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE,kCAAkC;4BAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACjE,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,8CAA8C;oBAC9C,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAClE,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,MAAM,sDAAsD,CAAC,CAAC;gBACnG,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;gBAExB,wDAAwD;gBACxD,kEAAkE;gBAClE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,MAAM,EAAE,+DAA+D;oBAC7E,OAAO,EAAE,mBAAmB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACpF,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,6CAA6C;gBAC7C,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,qBAAqB,KAAK,+BAA+B,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,iFAAiF,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,SAAqB;QAC9C,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;QACzE,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAEzE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,SAAS,CAAC,MAAM,YAAY,QAAQ,OAAO,CAAC,CAAC;QAEhF,IAAI,CAAC;YACH,IAAI,OAAqB,CAAC;YAE1B,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBAC5B,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,aAAa,SAAS,CAAC,MAAM,kCAAkC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpH,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,aAAa,SAAS,CAAC,MAAM,mCAAmC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrH,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACpF,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;YAExB,8BAA8B;YAC9B,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChC,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,EAAE,CAAC,cAAc,CACpB,QAAQ,CAAC,IAAI,EACb,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,aAAa,CACrB,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAErD,qCAAqC;YACrC,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAChC,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,aAAa,EAAE,CAAC;aACjB,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC;IAID,kBAAkB;IAClB,sBAAsB;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC;gBACf,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,SAAiB;QACnC,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAC1C,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;QACrD,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC;IACpD,CAAC;CACF"}