import fg from 'fast-glob';
import { promises as fs } from 'fs';
import { nanoid } from 'nanoid';
export class GlobTool {
    async execute(params) {
        const id = nanoid();
        const startTime = Date.now();
        try {
            const results = await this.findFiles(params);
            const output = this.formatResults(results, params.pattern);
            return {
                id,
                success: true,
                output,
                executionTime: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                executionTime: Date.now() - startTime
            };
        }
    }
    async findFiles(params) {
        const options = {
            cwd: params.cwd || process.cwd(),
            absolute: params.absolute || false,
            dot: false, // Don't include hidden files by default
            ignore: [
                'node_modules/**',
                '.git/**',
                'dist/**',
                'build/**',
                'coverage/**',
                '.next/**',
                '.nuxt/**',
                'target/**',
                '__pycache__/**',
                '*.log'
            ]
        };
        try {
            const files = await fg(params.pattern, options);
            const maxResults = params.maxResults || 100;
            // Get file stats for sorting by modification time
            const fileMatches = await Promise.all(files.slice(0, maxResults * 2).map(async (file) => {
                try {
                    const fullPath = options.absolute ? file : `${options.cwd}/${file}`;
                    const stat = await fs.stat(fullPath);
                    return {
                        path: file,
                        modifiedTime: stat.mtime.getTime(),
                        size: stat.size,
                        isDirectory: stat.isDirectory()
                    };
                }
                catch {
                    // If we can't stat the file, include it anyway with current time
                    return {
                        path: file,
                        modifiedTime: Date.now(),
                        size: 0,
                        isDirectory: false
                    };
                }
            }));
            // Sort by modification time (newest first) and limit results
            fileMatches.sort((a, b) => b.modifiedTime - a.modifiedTime);
            return fileMatches.slice(0, maxResults);
        }
        catch (error) {
            throw new Error(`Glob pattern error: ${error}`);
        }
    }
    formatResults(results, pattern) {
        if (results.length === 0) {
            return `No files found matching pattern: ${pattern}`;
        }
        const output = [];
        output.push(`Found ${results.length} files matching pattern: ${pattern}\n`);
        // Group by type
        const files = results.filter(r => !r.isDirectory);
        const directories = results.filter(r => r.isDirectory);
        if (directories.length > 0) {
            output.push('📁 Directories:');
            directories.forEach(dir => {
                const modifiedDate = new Date(dir.modifiedTime).toLocaleDateString();
                output.push(`   ${dir.path} (modified: ${modifiedDate})`);
            });
            output.push('');
        }
        if (files.length > 0) {
            output.push('📄 Files:');
            files.forEach(file => {
                const modifiedDate = new Date(file.modifiedTime).toLocaleDateString();
                const sizeStr = this.formatFileSize(file.size);
                output.push(`   ${file.path} (${sizeStr}, modified: ${modifiedDate})`);
            });
        }
        return output.join('\n');
    }
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
    }
    getDescription() {
        return `Fast file pattern matching tool that finds files by name and pattern.
    
Usage:
- Finding files by name patterns
- Locating specific file types
- Directory structure analysis

Examples:
- **/*.ts - Find all TypeScript files
- **/package.json - Find all package.json files
- src/**/*.test.* - Find all test files in src directory

Patterns:
- * matches any characters except /
- ** matches any characters including /
- ? matches any single character
- [abc] matches any character in the set
- {a,b} matches either a or b

Notes:
- Returns matching paths sorted by modification time (newest first)
- Automatically ignores common build directories and hidden files
- Supports both relative and absolute path output
- Includes file size and modification date information`;
    }
    validateParams(params) {
        return (typeof params === 'object' &&
            typeof params.pattern === 'string' &&
            params.pattern.trim().length > 0 &&
            (params.cwd === undefined || typeof params.cwd === 'string') &&
            (params.absolute === undefined || typeof params.absolute === 'boolean') &&
            (params.maxResults === undefined || typeof params.maxResults === 'number'));
    }
}
//# sourceMappingURL=glob.js.map