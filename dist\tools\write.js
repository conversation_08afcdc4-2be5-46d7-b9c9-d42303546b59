import { promises as fs } from 'fs';
import { dirname } from 'path';
import { nanoid } from 'nanoid';
export class WriteTool {
    async execute(params) {
        const id = nanoid();
        const startTime = Date.now();
        try {
            await this.writeFile(params);
            const output = `Successfully wrote ${params.content.length} characters to ${params.path}`;
            return {
                id,
                success: true,
                output,
                executionTime: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : String(error),
                executionTime: Date.now() - startTime
            };
        }
    }
    async writeFile(params) {
        const encoding = params.encoding || 'utf8';
        const createDirs = params.createDirectories !== false; // Default to true
        try {
            // Check if file exists
            const fileExists = await this.fileExists(params.path);
            if (createDirs) {
                // Ensure directory exists
                const dir = dirname(params.path);
                await fs.mkdir(dir, { recursive: true });
            }
            // Write the file
            await fs.writeFile(params.path, params.content, encoding);
            // Log the operation
            if (fileExists) {
                console.log(`📝 Overwrote existing file: ${params.path}`);
            }
            else {
                console.log(`📄 Created new file: ${params.path}`);
            }
        }
        catch (error) {
            if (error instanceof Error) {
                if (error.message.includes('ENOENT')) {
                    throw new Error(`Directory does not exist: ${dirname(params.path)}`);
                }
                else if (error.message.includes('EACCES')) {
                    throw new Error(`Permission denied: ${params.path}`);
                }
                else if (error.message.includes('EISDIR')) {
                    throw new Error(`Path is a directory, not a file: ${params.path}`);
                }
            }
            throw error;
        }
    }
    async fileExists(path) {
        try {
            await fs.access(path);
            return true;
        }
        catch {
            return false;
        }
    }
    getDescription() {
        return `File writing tool that creates or updates files in the filesystem.
    
Usage:
- Creating new files
- Completely replacing file content
- Generating code, configuration, or documentation files

Examples:
- Create new source code files
- Generate configuration files
- Write documentation or README files

Features:
- Automatically creates parent directories if they don't exist
- Supports different text encodings (default: utf8)
- Provides feedback on whether file was created or overwritten
- Handles permission and path validation errors

Important Notes:
- This tool will overwrite existing files completely
- Use the edit tool for partial file modifications
- Always confirm before overwriting important files
- Supports proper line endings for the target platform`;
    }
    validateParams(params) {
        return (typeof params === 'object' &&
            typeof params.path === 'string' &&
            params.path.trim().length > 0 &&
            typeof params.content === 'string' &&
            (params.encoding === undefined || typeof params.encoding === 'string') &&
            (params.createDirectories === undefined || typeof params.createDirectories === 'boolean'));
    }
    // Utility method to validate file path
    validatePath(path) {
        if (!path || path.trim().length === 0) {
            return { valid: false, error: 'Path cannot be empty' };
        }
        // Check for invalid characters (basic validation)
        const invalidChars = /[<>:"|?*]/;
        if (invalidChars.test(path)) {
            return { valid: false, error: 'Path contains invalid characters' };
        }
        // Check for relative path traversal
        if (path.includes('..')) {
            return { valid: false, error: 'Path traversal not allowed' };
        }
        return { valid: true };
    }
    // Utility method to get file info
    async getFileInfo(path) {
        try {
            const stat = await fs.stat(path);
            return {
                exists: true,
                size: stat.size,
                modified: stat.mtime
            };
        }
        catch {
            return { exists: false };
        }
    }
    // Utility method to backup existing file
    async backupFile(path) {
        try {
            const info = await this.getFileInfo(path);
            if (!info.exists) {
                return null;
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = `${path}.backup.${timestamp}`;
            const content = await fs.readFile(path, 'utf8');
            await fs.writeFile(backupPath, content, 'utf8');
            return backupPath;
        }
        catch {
            return null;
        }
    }
}
//# sourceMappingURL=write.js.map