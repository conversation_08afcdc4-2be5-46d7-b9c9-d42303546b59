{"version": 3, "file": "retry-logic.js", "sourceRoot": "", "sources": ["../../src/core/retry-logic.ts"], "names": [], "mappings": "AAAA,OAAO,EAA4B,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAEzE,MAAM,OAAO,YAAY;IACf,cAAc,GAAiB;QACrC,WAAW,EAAE,CAAC,EAAE,mCAAmC;QACnD,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,KAAK,EAAE,gEAAgE;QACjF,eAAe,EAAE,CAAC;QAClB,MAAM,EAAE,IAAI;KACb,CAAC;IAEM,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IACjD,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IAEzD,KAAK,CAAC,gBAAgB,CACpB,SAA2B,EAC3B,UAAiC,EAAE,EACnC,OAAqC;QAErC,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACpD,MAAM,KAAK,GAAe;YACxB,OAAO,EAAE,CAAC;YACV,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,KAAK,CAAC,OAAO,EAAE,CAAC;YAEhB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,KAAK,CAAC,SAAS,GAAG,KAAc,CAAC;gBACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE5C,8BAA8B;gBAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAc,CAAC,EAAE,CAAC;oBAC3C,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,gDAAgD;gBAChD,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACtC,MAAM,IAAI,UAAU,CAClB,0BAA0B,IAAI,CAAC,WAAW,cAAc,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,EACjF,sBAAsB,EACtB,KAAK,CACN,CAAC;gBACJ,CAAC;gBAED,mCAAmC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEvD,qBAAqB;gBACrB,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;gBAED,uBAAuB;gBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,2DAA2D;QAC3D,MAAM,KAAK,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC/D,CAAC;IAEO,gBAAgB,CAAC,KAAY;QACnC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAEjD,sCAAsC;QACtC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAEvC,iDAAiD;QACjD,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;YACnC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAClC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAClC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;YACrC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC;YACtC,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACvC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;YACnC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACvC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAClC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0CAA0C;QAC1C,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,uBAAuB,CAAC;YAC9C,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;YACpC,YAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC;YAC5C,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,IAAI,YAAY,CAAC,QAAQ,CAAC,yBAAyB,CAAC;YAChD,YAAY,CAAC,QAAQ,CAAC,gCAAgC,CAAC;YACvD,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YACzC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,IAAI,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC;YACtC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;YACrC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACjC,YAAY,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;YACpC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;YACrC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAClC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAClC,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YAC7C,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAC1C,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,0CAA0C;QAC1C,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC,SAAS,CAAC;QACzB,CAAC;QAED,wEAAwE;QACxE,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,mBAAmB,CAAC,YAAoB;QAC9C,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,gBAAgB,CAAC,YAAoB;QAC3C,2DAA2D;QAC3D,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,SAAS,CAAC;QAC9F,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,YAAY,CAAC;QACvF,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACvD,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,OAAO,cAAc,CAAC;QACtD,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,OAAO,cAAc,CAAC;QACtD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,OAAqB;QAC3D,qEAAqE;QACrE,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAE/E,kBAAkB;QAClB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,wCAAwC;QACxC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,sCAAsC;IACtC,KAAK,CAAC,oBAAoB,CACxB,SAA2B,EAC3B,WAA0C;QAE1C,OAAO,IAAI,CAAC,gBAAgB,CAC1B,SAAS,EACT;YACE,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,IAAI;SACb,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,WAAW,EAAE,CAAC;gBACnE,0CAA0C;gBAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACnE,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,YAAoB;QAC5C,sDAAsD;QACtD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC5D,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB;IACtF,CAAC;IAED,oEAAoE;IACpE,KAAK,CAAC,uBAAuB,CAC3B,SAA2B,EAC3B,cAAwD;QAExD,OAAO,IAAI,CAAC,gBAAgB,CAC1B,SAAS,EACT;YACE,WAAW,EAAE,CAAC,EAAE,mCAAmC;YACnD,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,IAAI;SACb,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,cAAc,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,6CAA6C;IAC7C,KAAK,CAAC,uBAAuB,CAC3B,gBAAkC,EAClC,qBAA8C,EAC9C,oBAAuE;QAEvE,8BAA8B;QAC9B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;gBACnD,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,CAAC;gBAClB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,YAAY,EAAE,CAAC;YACtB,qCAAqC;YACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC;oBACH,IAAI,oBAAoB,EAAE,CAAC;wBACzB,oBAAoB,CAAC,CAAC,EAAE,YAAqB,CAAC,CAAC;oBACjD,CAAC;oBAED,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAAE,EAAE;wBAC5D,WAAW,EAAE,CAAC,EAAE,kCAAkC;wBAClD,SAAS,EAAE,GAAG;wBACd,QAAQ,EAAE,IAAI;wBACd,eAAe,EAAE,GAAG;wBACpB,MAAM,EAAE,IAAI;qBACb,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,gBAAgB,EAAE,CAAC;oBAC1B,+BAA+B;oBAC/B,SAAS;gBACX,CAAC;YACH,CAAC;YAED,qDAAqD;YACrD,MAAM,IAAI,UAAU,CAClB,6BAA6B,qBAAqB,CAAC,MAAM,qCAAqC,YAAY,EAAE,EAC5G,yBAAyB,EACzB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED,0CAA0C;IAC1C,KAAK,CAAC,mBAAmB,CACvB,SAA2B,EAC3B,gBAAwB,EACxB,UAA2D;QAG3D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE;gBACpD,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;gBAC1D,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC;gBACtD,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,CAAC;gBAClB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,gBAAgB;YAChB,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAEpC,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBAC1D,UAAU,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,6BAA6B;YAC7B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAe;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5D,kEAAkE;QAClE,IAAI,YAAY,GAAG,YAAY,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,oBAAoB;QACnE,IAAI,YAAY,GAAG,YAAY;YAAE,OAAO,CAAC,CAAC,CAAC,wBAAwB;QACnE,OAAO,CAAC,CAAC,CAAC,kCAAkC;IAC9C,CAAC;IAEO,oBAAoB,CAAC,OAAe;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5D,oDAAoD;QACpD,IAAI,YAAY,GAAG,EAAE;YAAE,OAAO,IAAI,CAAC,CAAC,oBAAoB;QACxD,IAAI,YAAY,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC,CAAC,wBAAwB;QAC3D,OAAO,IAAI,CAAC,CAAC,kCAAkC;IACjD,CAAC;IAEO,YAAY,CAAC,OAAe;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAEO,YAAY,CAAC,OAAe;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,YAAY,GAAG,YAAY,CAAC;QAE1C,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,oDAAoD;IACpD,kBAAkB;QAMhB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACvG,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACxG,MAAM,eAAe,GAAG,aAAa,GAAG,cAAc,CAAC;QACvD,MAAM,kBAAkB,GAAG,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtF,OAAO;YACL,eAAe,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC;YAC9C,eAAe,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC;YAC9C,eAAe;YACf,kBAAkB;SACnB,CAAC;IACJ,CAAC;IAED,2DAA2D;IAC3D,iBAAiB;QACf,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF"}