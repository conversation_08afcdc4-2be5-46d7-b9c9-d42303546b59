{"version": 3, "file": "retry-logic.js", "sourceRoot": "", "sources": ["../../src/core/retry-logic.ts"], "names": [], "mappings": "AAAA,OAAO,EAA4B,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAEzE,MAAM,OAAO,YAAY;IACf,cAAc,GAAiB;QACrC,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,KAAK;QACf,eAAe,EAAE,CAAC;QAClB,MAAM,EAAE,IAAI;KACb,CAAC;IAEF,KAAK,CAAC,gBAAgB,CACpB,SAA2B,EAC3B,UAAiC,EAAE,EACnC,OAAqC;QAErC,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACpD,MAAM,KAAK,GAAe;YACxB,OAAO,EAAE,CAAC;YACV,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,KAAK,CAAC,OAAO,EAAE,CAAC;YAEhB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;gBACjC,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,KAAK,CAAC,SAAS,GAAG,KAAc,CAAC;gBACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE5C,8BAA8B;gBAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAc,CAAC,EAAE,CAAC;oBAC3C,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,gDAAgD;gBAChD,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACtC,MAAM,IAAI,UAAU,CAClB,0BAA0B,IAAI,CAAC,WAAW,cAAc,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,EACjF,sBAAsB,EACtB,KAAK,CACN,CAAC;gBACJ,CAAC;gBAED,mCAAmC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEvD,qBAAqB;gBACrB,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;gBAED,uBAAuB;gBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,2DAA2D;QAC3D,MAAM,KAAK,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC/D,CAAC;IAEO,gBAAgB,CAAC,KAAY;QACnC,iBAAiB;QACjB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YACpC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gBAAgB;QAChB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YACpC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sBAAsB;QACtB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gCAAgC;QAChC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC;YAC7C,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAC,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;YACrC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;YACtC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC,SAAS,CAAC;QACzB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,OAAqB;QAC3D,qEAAqE;QACrE,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAE/E,kBAAkB;QAClB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,wCAAwC;QACxC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,sCAAsC;IACtC,KAAK,CAAC,oBAAoB,CACxB,SAA2B,EAC3B,WAA0C;QAE1C,OAAO,IAAI,CAAC,gBAAgB,CAC1B,SAAS,EACT;YACE,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,IAAI;SACb,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,WAAW,EAAE,CAAC;gBACnE,0CAA0C;gBAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACnE,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,YAAoB;QAC5C,sDAAsD;QACtD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC5D,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB;IACtF,CAAC;IAED,2CAA2C;IAC3C,KAAK,CAAC,uBAAuB,CAC3B,SAA2B,EAC3B,cAAwD;QAExD,OAAO,IAAI,CAAC,gBAAgB,CAC1B,SAAS,EACT;YACE,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,IAAI;SACb,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,cAAc,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACtC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;CACF"}