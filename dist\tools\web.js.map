{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/tools/web.ts"], "names": [], "mappings": "AAAA,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AAEnC,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAQhC,MAAM,OAAO,OAAO;IACV,MAAM,CAAgB;IAE9B;QACE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,YAAY,EAAE,qHAAqH;aACpI;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAqB;QACjC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAEzD,OAAO;gBACL,EAAE;gBACF,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE;gBACF,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,MAAqB;QAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC;YACH,uEAAuE;YACvE,MAAM,SAAS,GAAG,iCAAiC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC;YAE5H,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE;gBAChD,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;aACjC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,yBAAyB;YACzB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,gBAAgB;oBACvC,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;oBAC3B,OAAO,EAAE,IAAI,CAAC,QAAQ;oBACtB,MAAM,EAAE,IAAI,CAAC,cAAc,IAAI,YAAY;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;oBAC9E,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACjC,OAAO,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC;4BACpC,GAAG,EAAE,KAAK,CAAC,QAAQ;4BACnB,OAAO,EAAE,KAAK,CAAC,IAAI;4BACnB,MAAM,EAAE,YAAY;yBACrB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,mEAAmE;YACnE,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,eAAe;oBACtB,GAAG,EAAE,EAAE;oBACP,OAAO,EAAE,IAAI,CAAC,MAAM;oBACpB,MAAM,EAAE,YAAY;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,iEAAiE;YACjE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC7D,CAAC;YAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAEtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uCAAuC;YACvC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,UAAkB;QAC5D,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,SAAS,GAAG,uCAAuC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YAErF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE;gBAChD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,KAAa,EAAE,OAAY,EAAE,EAAE;gBAChD,IAAI,KAAK,IAAI,UAAU;oBAAE,OAAO,KAAK,CAAC;gBAEtC,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;gBAC9D,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACjE,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;gBAEhE,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC;wBACX,KAAK;wBACL,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG;wBAChD,OAAO;wBACP,MAAM,EAAE,YAAY;qBACrB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,CAAC,CAAC,qBAAqB;YACpC,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,8CAA8C;YAC9C,OAAO,CAAC;oBACN,KAAK,EAAE,oBAAoB;oBAC3B,GAAG,EAAE,EAAE;oBACP,OAAO,EAAE,iDAAiD,KAAK,8HAA8H,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACxN,MAAM,EAAE,QAAQ;iBACjB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,2CAA2C;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAE3C,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,EAAE,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC7E,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,kCAAkC;QAClC,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACjE,CAAC;IAEO,aAAa,CAAC,OAAuB,EAAE,KAAa;QAC1D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,gCAAgC,KAAK,EAAE,CAAC;QACjD,CAAC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,IAAI,CAAC,CAAC;QAEjD,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YAEjD,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,uBAAuB;gBACvB,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO;qBAChC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;qBACpB,IAAI,EAAE;qBACN,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAErB,MAAM,CAAC,IAAI,CAAC,SAAS,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,cAAc;QACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;yCAwB8B,CAAC;IACxC,CAAC;IAED,cAAc,CAAC,MAAW;QACxB,OAAO,CACL,OAAO,MAAM,KAAK,QAAQ;YAC1B,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ;YAChC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;YAC9B,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC;YAC1E,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CACrE,CAAC;IACJ,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,QAAQ,CAAC,GAAW;QACxB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC1C,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEtC,gBAAgB;YAChB,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;gBACzB,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;gBAC7B,UAAU,CAAC;YAExB,oCAAoC;YACpC,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;iBAC7B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;iBACpB,IAAI,EAAE;iBACN,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAEtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;CACF"}