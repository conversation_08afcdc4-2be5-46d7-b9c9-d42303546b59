import { Tool<PERSON>all, ToolResult, ToolType, ExecutionContext } from '../types/index.js';
export declare class ToolExecutor {
    private bashTool;
    private grepTool;
    private globTool;
    private writeTool;
    private editTool;
    private webTool;
    private retryManager;
    constructor();
    executeTool(toolCall: ToolCall, context: ExecutionContext): Promise<ToolResult>;
    executeToolsParallel(toolCalls: ToolCall[], context: ExecutionContext): Promise<ToolResult[]>;
    executeToolsSequential(toolCalls: ToolCall[], context: ExecutionContext): Promise<ToolResult[]>;
    private validateToolCall;
    private isCriticalTool;
    getToolDescription(toolType: ToolType): string;
    getAllToolDescriptions(): Record<ToolType, string>;
    determineExecutionStrategy(toolCalls: ToolCall[]): 'parallel' | 'sequential';
    estimateExecutionTime(toolCalls: ToolCall[]): number;
}
//# sourceMappingURL=tool-executor.d.ts.map